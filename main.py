from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api.endpoints import auth_api, message_api, endpoint, document_api, summary_api
from app.api.chat import chat_like_api
from app.api.chat import chat_message_api
from app.api.endpoints import tool_api
from app.api.endpoints import image_api
from app.api.chart import chart_api
from app.core.config import settings


app = FastAPI(
    title="MengChat - 企业微信AI助手",
    description="企业微信AI助手",
    version="1.0.0"
)

# CORS设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
    allow_credentials=True,
)

# 注册路由
app.include_router(auth_api.router, tags=["auth_api"])
app.include_router(message_api.router, tags=["message_api"])
app.include_router(endpoint.router, tags=["endpoint"])
app.include_router(chat_like_api.router, tags=["chat_like_api"])
app.include_router(chat_message_api.router, tags=["chat_message_api"])
app.include_router(document_api.router, tags=["document_api"])
app.include_router(summary_api.router, tags=["summary_api"])
app.include_router(image_api.router, tags=["image_api"])
app.include_router(tool_api.router, tags=["tool_api"])
app.include_router(chart_api.router, tags=["chart_api"])

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        app="main:app",  # 使用字符串引用应用
        host="0.0.0.0",
        port=settings.SERVER_PORT,
        workers=4,  # 使用多个工作进程
        timeout_keep_alive=65,  # 保持连接超时时间
        backlog=2048,  # 增加等待连接队列大小
        log_level="info"
    )