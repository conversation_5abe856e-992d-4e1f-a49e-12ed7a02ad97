# 项目名称：企业微信AI助手-MengChat

## 项目概述
企业微信 AI 助手是一个基于 FastAPI 的智能消息处理系统，集成了 MengChat AI 接口，支持多种消息类型的处理和智能回复。

## 项目功能

### 1. 消息处理
- 接收企业微信应用消息
  - 支持文本消息
  - 支持图片消息
  - 支持图文混合消息
- 消息安全性
  - XML消息加解密
  - 签名验证
  - 安全令牌校验

### 2. AI 集成
- 集成 MengChat API
  - 文本消息智能回复
  - 图片内容分析
  - URL 智能提取和处理

### 3. 消息转换
- Markdown 格式解析
  - 支持图片链接提取 ![alt](url)
  - 支持普通 URL 提取
- 图文消息生成
  - 自动提取标题和描述
  - 支持图片预览
  - 智能排版

## 技术栈

### 1. 后端框架
- Python 3.8+
- FastAPI：Web框架
- Pydantic：数据验证
- uvicorn：ASGI服务器

### 2. 工具库
- loguru：日志记录
- requests：HTTP客户端
- python-multipart：表单处理
- python-jose：JWT处理
- cryptography：加密解密

### 3. 开发工具
- Visual Studio Code
- Git：版本控制
- Postman：API测试

## 项目结构
mengchat-app/
├── app/
│   ├── api/
│   │   ├── endpoints/
│   │   │   ├── auth.py      # 认证和消息处理
│   │   │   └── push.py      # 消息推送
│   │   └── __init__.py
│   ├── core/
│   │   ├── config.py        # 配置管理
│   │   └── security.py      # 安全相关
│   ├── models/
│   │   ├── message.py       # 消息模型
│   │   └── __init__.py
│   ├── services/
│   │   ├── wechat.py        # 企业微信服务
│   │   ├── mengchat.py      # MengChat服务
│   │   └── __init__.py
│   ├── utils/
│   │   ├── wechat_crypto.py # 加解密工具
│   │   └── __init__.py
│   └── __init__.py
├── tests/                    # 测试目录
├── .env                      # 环境变量
├── .gitignore
├── requirements.txt          # 依赖管理
└── README.md

## 关键配置
```python
# 企业微信配置
CORP_ID = "wx682fa921f5b89317"
AGENT_ID = "1000081"
SECRET = "your_secret"
TOKEN = "your_token"
ENCODING_AES_KEY = "your_encoding_aes_key"

# MengChat配置
MENGCHAT_API_URL = "http://10.28.8.72:3020/api"
MENGCHAT_API_KEY = "your_api_key"
```

## 核心功能实现

### 1. 消息接收和解密
```python
@router.post("/callback")
async def callback_message(
    request: Request,
    msg_signature: str,
    timestamp: str,
    nonce: str,
    crypto: WXBizMsgCrypt = Depends(get_crypto)
)
```

### 2. 消息解析和处理
- XML解析
- 消息类型判断
- 内容提取

### 3. AI处理
- 文本分析
- 图片识别
- 智能回复

### 4. 消息发送
- 文本消息
- 图文消息
- 媒体消息

## 安全性考虑
1. 消息加解密
2. 签名验证
3. Token 认证
4. 异常处理

## 部署说明
1. 环境要求
   - Python 3.8+
   - pip 包管理器
   - 企业微信应用配置
   - MengChat API 访问权限

2. 安装步骤
   ```bash
   # 克隆项目
   git clone [repository_url]

   # 安装依赖
   pip install -r requirements.txt

   # 配置环境变量
   cp .env.example .env
   # 编辑 .env 文件

   # 启动服务
   uvicorn app.main:app --host 0.0.0.0 --port 8000
   ```

## 测试
1. 单元测试
2. 接口测试
3. 集成测试

## 维护和监控
1. 日志管理
2. 性能监控
3. 错误追踪

## 注意事项
1. 保护好密钥和令牌
2. 定期更新依赖
3. 监控系统日志
4. 做好异常处理