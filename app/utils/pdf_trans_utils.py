import os
import asyncio
import tempfile
from typing import Dict, Optional, Callable
from loguru import logger
from pdf2docx import Converter

# 导入word翻译工具
from app.utils.word_trans_utils import (
    translate_docx,
    register_progress_callback,
    remove_progress_callback,
    translate_filename
)

# --- 定义全局进度回调字典 ---
_progress_callbacks = {}

# --- 注册进度回调函数 ---
def register_pdf_progress_callback(task_id, callback_func):
    """
    注册PDF翻译任务进度回调函数

    Args:
        task_id: 任务ID
        callback_func: 回调函数，接收进度信息字典
    """
    _progress_callbacks[task_id] = callback_func

# --- 移除进度回调函数 ---
def remove_pdf_progress_callback(task_id):
    """
    移除PDF翻译任务进度回调函数

    Args:
        task_id: 任务ID
    """
    if task_id in _progress_callbacks:
        del _progress_callbacks[task_id]

# --- PDF转Word功能 ---
async def convert_pdf_to_docx(pdf_path: str, docx_path: str) -> bool:
    """
    将PDF文件转换为Word文档
    
    Args:
        pdf_path: PDF文件路径
        docx_path: 输出的Word文档路径
        
    Returns:
        bool: 转换是否成功
    """
    try:
        # 使用运行在执行器中的阻塞函数
        loop = asyncio.get_running_loop()
        
        def _convert():
            # 创建转换器
            cv = Converter(pdf_path)
            # 执行转换
            cv.convert(docx_path)
            # 关闭转换器
            cv.close()
            return True
            
        # 在执行器中运行阻塞操作
        result = await loop.run_in_executor(None, _convert)
        logger.info(f"PDF转Word成功: {pdf_path} -> {docx_path}")
        return result
    except Exception as e:
        logger.error(f"PDF转Word失败: {e}")
        return False

# --- 异步PDF翻译主函数 ---
async def translate_pdf(input_pdf_path: str, output_pdf_path: str, task_id: Optional[str] = None, 
                        progress_callback: Optional[Callable] = None, 
                        source_language: Optional[str] = None, 
                        target_language: Optional[str] = None) -> str:
    """
    异步翻译PDF文档，通过先转换为Word文档，然后翻译Word文档，最后再转回PDF
    
    Args:
        input_pdf_path: 输入PDF文档路径
        output_pdf_path: 输出PDF文档路径
        task_id: 任务ID，用于关联进度回调
        progress_callback: 进度回调函数，接收进度信息
        source_language: 源语言代码
        target_language: 目标语言代码
        
    Returns:
        str: 翻译完成状态信息
    """
    # 如果提供了task_id但没有提供progress_callback，使用全局回调字典
    if task_id and not progress_callback:
        if task_id in _progress_callbacks:
            progress_callback = _progress_callbacks[task_id]
    
    # 更新进度的辅助函数
    def update_progress(progress, message, status="processing"):
        if progress_callback:
            progress_callback({
                "status": status,
                "progress": progress,
                "message": message
            })
        logger.info(f"[{progress}%] {message}")
    
    try:
        # 0. 检查输入文件是否存在
        if not os.path.exists(input_pdf_path):
            error_msg = f"输入文件不存在: {input_pdf_path}"
            update_progress(0, error_msg, status="failed")
            return f"错误: {error_msg}"
        
        # 1. 创建临时文件用于存储中间Word文档
        update_progress(5, "准备转换PDF到Word")
        temp_docx_path = f"./temp/inputs/temp_converted_{os.path.basename(input_pdf_path)}.docx"
        translated_docx_path = output_pdf_path
        
        # 2. 将PDF转换为Word
        update_progress(10, "正在将PDF转换为Word")
        conversion_success = await convert_pdf_to_docx(input_pdf_path, temp_docx_path)
        if not conversion_success:
            error_msg = f"PDF转Word失败: {input_pdf_path}"
            update_progress(0, error_msg, status="failed")
            return f"错误: {error_msg}"
        
        # 3. 翻译Word文档
        update_progress(30, "开始翻译Word文档")
        
        # 创建一个包装的进度回调，将进度范围从30%-90%映射到word翻译的0%-100%
        def word_progress_wrapper(progress_info):
            if progress_callback:
                # 将Word翻译的进度(0-100)映射到PDF翻译的进度(30-90)
                mapped_progress = 30 + (progress_info["progress"] * 0.6)
                progress_info["progress"] = mapped_progress
                progress_callback(progress_info)
        
        # 调用word_trans_utils中的翻译函数
        word_translation_result = await translate_docx(
            temp_docx_path, 
            translated_docx_path,
            task_id=f"{task_id}_word" if task_id else None,
            progress_callback=word_progress_wrapper,
            source_language=source_language,
            target_language=target_language
        )
        
        if "错误" in word_translation_result:
            update_progress(0, word_translation_result, status="failed")
            return word_translation_result
        
        # 4. 清理临时文件
        try:
            os.remove(temp_docx_path)
            # os.remove(translated_docx_path)
        except Exception as e:
            logger.warning(f"清理临时文件失败: {str(e)}")
        
        # 6. 完成
        update_progress(100, "PDF翻译完成", status="completed")
        return "PDF翻译成功完成"
        
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        error_msg = f"PDF处理过程中发生错误: {str(e)}"
        update_progress(0, error_msg, status="failed")
        logger.error(error_msg)
        logger.error(error_trace)
        return f"错误: {str(e)}"

# --- 翻译PDF文件名 ---
async def translate_pdf_filename(filename: str, source_language: str, target_language: str) -> str:
    """
    翻译PDF文件名
    
    Args:
        filename: 原始文件名
        source_language: 源语言代码
        target_language: 目标语言代码
        
    Returns:
        str: 翻译后的文件名
    """
    return await translate_filename(filename, source_language, target_language)

# --- 主程序入口 ---
async def main(input_file, output_file, source_language="zh", target_language="en"):
    """测试PDF翻译功能的主函数"""
    result = await translate_pdf(
        input_file, 
        output_file, 
        source_language=source_language, 
        target_language=target_language
    )
    print(result)

if __name__ == "__main__":
    input_file = './example.pdf'
    output_file = './example_translated.pdf'
    
    if not input_file or not output_file:
        logger.error("输入和输出文件路径不能为空。")
    elif not os.path.exists(input_file):
         logger.error(f"错误：输入文件 '{input_file}' 不存在。")
    else:
        asyncio.run(main(input_file, output_file)) 