import requests
from loguru import logger
from typing import <PERSON><PERSON>, Dict, Any, Optional
from fastapi import UploadFile
import os


async def upload_file_to_ftp(
    file: UploadFile,
    target_url: str = "http://netcore.groupama-sdig.com:30310/backend_shop/file/ftp/upload"
) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
    """
    将文件上传到FTP服务器
    
    Args:
        file: 上传的文件
        target_url: 目标FTP上传接口URL
        
    Returns:
        Tuple[bool, str, Optional[Dict[str, Any]]]: 
            - 是否成功
            - 消息
            - 成功时返回的数据，包含imgUrl等信息
    """
    try:
        # 读取上传的文件内容
        contents = await file.read()
        
        # 准备文件对象
        files = {
            'file': (file.filename, contents, file.content_type)
        }
        
        # 发送请求到目标接口
        logger.info(f"转发文件到FTP接口: {target_url}")
        response = requests.post(target_url, files=files)
        
        # 检查响应状态
        if response.status_code != 200:
            logger.error(f"FTP接口返回错误: {response.status_code}, {response.text}")
            return False, f"FTP接口返回错误: {response.status_code}", None
        
        # 解析响应数据
        response_data = response.json()
        if "data" not in response_data or "imgUrl" not in response_data["data"]:
            logger.error(f"FTP接口返回数据格式异常: {response_data}")
            return False, "FTP接口返回数据格式异常", None
            
        return True, "文件上传成功", response_data["data"]
        
    except Exception as e:
        error_msg = f"文件上传处理失败: {str(e)}"
        logger.error(error_msg)
        return False, error_msg, None


async def upload_local_file_to_ftp(
    file_path: str,
    file_name: str = None,
    content_type: str = "application/octet-stream",
    target_url: str = "http://netcore.groupama-sdig.com:30310/backend_shop/file/ftp/upload"
) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
    """
    将本地文件上传到FTP服务器
    
    Args:
        file_path: 本地文件路径
        file_name: 文件名称，如果为None则使用file_path的文件名
        content_type: 文件内容类型
        target_url: 目标FTP上传接口URL
        
    Returns:
        Tuple[bool, str, Optional[Dict[str, Any]]]: 
            - 是否成功
            - 消息
            - 成功时返回的数据，包含imgUrl等信息
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            error_msg = f"文件不存在: {file_path}"
            logger.error(error_msg)
            return False, error_msg, None
            
        # 如果没有提供文件名，使用路径中的文件名
        if file_name is None:
            file_name = os.path.basename(file_path)
        
        # 读取文件内容
        with open(file_path, 'rb') as f:
            contents = f.read()
        
        # 准备文件对象
        files = {
            'file': (file_name, contents, content_type)
        }
        
        # 发送请求到目标接口
        response = requests.post(target_url, files=files)
        
        # 检查响应状态
        if response.status_code != 200:
            logger.error(f"FTP接口返回错误: {response.status_code}, {response.text}")
            return False, f"FTP接口返回错误: {response.status_code}", None
        
        # 解析响应数据
        response_data = response.json()
        if "data" not in response_data or "imgUrl" not in response_data["data"]:
            logger.error(f"FTP接口返回数据格式异常: {response_data}")
            return False, "FTP接口返回数据格式异常", None
            
        logger.info(f"上传本地文件到FTP接口成功，本地文件：{file_path}，URL地址: {response_data['data']['imgUrl']}")
        return True, "文件上传成功", response_data["data"]
        
    except Exception as e:
        error_msg = f"本地文件上传处理失败: {str(e)}"
        logger.error(error_msg)
        return False, error_msg, None
