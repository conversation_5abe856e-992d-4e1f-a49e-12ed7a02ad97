"""文档加载器模块，用于加载Word、PDF和PPT文档。"""
import io
import fitz
import requests
from PIL import Image
from pathlib import Path
from PyPDF2 import PdfReader
from typing import List, Optional
from langchain_core.documents import Document
from langchain_community.document_loaders import PyPDFLoader, UnstructuredWordDocumentLoader, UnstructuredPowerPointLoader
from app.core.config import settings


class DocumentLoader:
    """文档加载器类，支持加载PDF、Word和PPT文档。"""

    @staticmethod
    def load_document(file_path: str) -> List[Document]:
        """根据文件类型加载文档。

        Args:
            file_path: 文档路径

        Returns:
            包含文档内容的Document对象列表

        Raises:
            ValueError: 不支持的文件类型
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 根据文件扩展名选择合适的加载器
        if file_path.suffix.lower() == ".pdf":
            return DocumentLoader._load_pdf(str(file_path))
        elif file_path.suffix.lower() in [".docx"]:
            return DocumentLoader._load_word(str(file_path))
        elif file_path.suffix.lower() in [".pptx"]:
            return DocumentLoader._load_powerpoint(str(file_path))
        else:
            raise ValueError(f"不支持的文件类型: {file_path.suffix}")
    
    @staticmethod
    def _load_pdf(file_path: str) -> List[Document]:
        """加载PDF文档。

        Args:
            file_path: PDF文件路径

        Returns:
            包含PDF内容的Document对象列表
        """
        # 使用标准加载器获取文本内容
        loader = PyPDFLoader(file_path)
        documents = loader.load()
        
        # 使用PyPDF2提取图片并进行OCR处理
        ocr_texts = DocumentLoader._extract_images_and_ocr(file_path)
        # 如果有OCR文本，将其添加到文档中
        if ocr_texts:
            # 创建一个新的Document对象，包含OCR识别的文本
            for page_num, text in ocr_texts.items():
                if text.strip():
                    # 查找对应页码的文档
                    page_found = False
                    for doc in documents:
                        if doc.metadata.get('page') == page_num:
                            # 将OCR文本添加到现有文档
                            doc.page_content += f"\n{text}\n"
                            page_found = True
                            break
                    
                    # 如果没有找到对应页码的文档，创建新文档
                    if not page_found and text.strip():
                        documents.append(Document(
                            page_content = f"\n{text}",
                            metadata={"source": file_path, "page": page_num, "ocr": True}
                        ))
        return documents
    
    @staticmethod
    def _load_word(file_path: str) -> List[Document]:
        """加载Word文档。

        Args:
            file_path: Word文件路径

        Returns:
            包含Word内容的Document对象列表
        """
        loader = UnstructuredWordDocumentLoader(file_path)
        return loader.load()
    
    @staticmethod
    def _load_powerpoint(file_path: str) -> List[Document]:
        """加载PowerPoint文档。

        Args:
            file_path: PowerPoint文件路径

        Returns:
            包含PowerPoint内容的Document对象列表
        """
        loader = UnstructuredPowerPointLoader(file_path)
        return loader.load()
    
    @staticmethod
    def _extract_images_and_ocr(pdf_path: str) -> Optional[dict]:
        """从PDF中提取图片并使用OCR API识别文本。
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            包含每页OCR文本的字典，键为页码（从0开始），值为OCR文本
            如果API不可用或处理过程中出错，返回None
        """
        try:
            reader = PdfReader(pdf_path)
            result = {}
            
            for page_num, page in enumerate(reader.pages):
                page_text = ""
                print(f"正在处理第 {page_num + 1} 页...")
                
                # 处理嵌入式图片
                embedded_image_found = False
                if "/XObject" in page:
                    print("正在识别嵌入图片中的文本...")
                    xobject = page["/XObject"].get_object()
                    for obj in xobject:
                        if xobject[obj]["/Subtype"] == "/Image":
                            try:
                                embedded_image_found = True
                                data = xobject[obj]._data
                                img = Image.open(io.BytesIO(data))
                                
                                # 将图片转换为字节流，以便发送到API
                                img_byte_arr = io.BytesIO()
                                img_byte_arr = img_byte_arr.getvalue()
                                
                                # 调用OCR API识别文本
                                text = DocumentLoader._call_ocr_api(img_byte_arr)
                                
                                if text.strip():
                                    page_text += text + "\n"
                            except Exception as e:
                                print(f"OCR处理嵌入图片时出错: {e}")
                
                # 检测是否为扫描页面
                # 如果页面没有文本内容或文本内容很少，可能是扫描件
                page_content = page.extract_text().strip()
                is_likely_scanned = len(page_content) < 100  # 如果页面文本少于100个字符，可能是扫描件
                
                # 如果页面可能是扫描件或者没有找到嵌入式图片，将整个页面作为图片处理
                if is_likely_scanned or not embedded_image_found:
                    try:
                        print("检测到可能的扫描页面，正在处理整页图片...")
                        # 使用PyMuPDF(fitz)将PDF页面转换为图片
                        pdf_document = fitz.open(pdf_path)
                        pdf_page = pdf_document[page_num]
                        
                        # 获取页面的像素图，使用较高的分辨率以提高OCR质量
                        text = ""
                        image_list = pdf_page.get_images(full=True)
                        for image_index, img in enumerate(image_list):
                            # 提取图像
                            xref = img[0]
                            base_image = pdf_document.extract_image(xref)
                            image_bytes = base_image["image"]

                            # 使用pytesseract对图像进行ocr
                            text += DocumentLoader._call_ocr_api(io.BytesIO(image_bytes).getvalue())

                        if text.strip():
                            # 如果已经有嵌入图片的文本，添加标记以区分
                            if page_text.strip():
                                page_text += "\n" + text + "\n"
                            else:
                                page_text = text
                    except Exception as e:
                        print(f"OCR处理整页扫描时出错: {e}")
                
                if page_text.strip():
                    result[page_num] = page_text
            return result if result else None
        except Exception as e:
            print(f"提取PDF图片时出错: {e}")
            return None
        
    @staticmethod
    def _call_ocr_api(image_data: bytes) -> str:
        """调用OCR API识别图片中的文本。
        
        Args:
            image_data: 图片二进制数据
            
        Returns:
            识别出的文本，如果识别失败则返回空字符串
        """
        try:
            # OCR API的URL，实际使用时需要替换为真实的API地址
            api_url = settings.OCR_API_URL
            
            # 准备上传的文件
            files = {
                'file': ('image.png', image_data, 'image/png')
            }

            data = {    
                'type': 1
            }
            
            # 可能需要的其他参数
            headers = {
                'Authorization': f'Bearer {settings.OCR_API_KEY}'  # 如果需要认证
            }
            
            # 发送POST请求
            response = requests.post(api_url, data=data, files=files, headers=headers, timeout=30)
            
            # 检查响应状态
            if response.status_code == 200:
                # 解析JSON响应
                result = response.json()
                return result['data']
            else:
                print(f"警告: API请求失败，状态码: {response.status_code}")
                return ""
                
        except Exception as e:
            print(f"调用OCR API时出错: {e}")
            return ""