"""文本处理模块，用于分割和处理文档文本。"""

from typing import List

from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter


class TextProcessor:
    """文本处理类，用于分割长文本。"""

    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 100):
        """初始化文本处理器。

        Args:
            chunk_size: 文本块大小
            chunk_overlap: 文本块重叠大小
        """
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separators=["\n\n", "\n", "。", "！", "？", ".", "!", "?", " ", ""]
        )
    
    def split_documents(self, documents: List[Document]) -> List[Document]:
        """分割文档为更小的文本块。

        Args:
            documents: 文档列表

        Returns:
            分割后的文档列表
        """
        return self.text_splitter.split_documents(documents)
    
    def combine_documents(self, documents: List[Document]) -> str:
        """将文档列表合并为单个文本字符串。

        Args:
            documents: 文档列表

        Returns:
            合并后的文本
        """
        return "\n\n".join([doc.page_content for doc in documents])