"""摘要生成模块，使用Ollama模型生成文档摘要。"""
from loguru import logger
from typing import List, Optional, Callable

from langchain_core.documents import Document
from langchain_ollama import ChatOllama
from langchain.chains.summarize import load_summarize_chain
from langchain.prompts import PromptTemplate
from tqdm import tqdm


class Summarizer:
    """摘要生成器类，使用Ollama模型生成摘要。"""

    def __init__(self, model_name: str = "qwen2.5:14b", temperature: float = 0.5):
        """初始化摘要生成器。

        Args:
            model_name: Ollama模型名称
            temperature: 生成温度，控制创造性
        """
        self.llm = ChatOllama(base_url="http://10.28.8.203:11434", model=model_name, temperature=temperature)
        
        # 创建摘要提示模板
        self.map_prompt_template = PromptTemplate(
            template="""请为以下文本生成简洁的摘要：

{text}

生成的摘要遵循以下规则：
- 摘要内容要全面，包含所有重要信息
- 摘要内容不要使用 markdown 格式
- 摘要内容要简洁，不要冗长，不超过200字
- 直接返回摘要内容，不解释，不以最终摘要：开头

生成摘要：""",
            input_variables=["text"]
        )
        
        self.combine_prompt_template = PromptTemplate(
            template="""以下是一系列文档片段的摘要：

{text}

请将这些摘要整合成一个连贯、全面且简洁的最终摘要。确保包含所有重要信息，但避免重复。
生成的摘要遵循以下规则：
- 摘要内容要全面，包含所有重要信息
- 摘要要简洁，不要冗长，不超过500字
- 摘要内容不要使用 markdown 格式
- 摘要内容要有逻辑，良好的组织结构，不要乱序
- 直接返回摘要内容，不解释，不以最终摘要：开头

生成摘要：""",
            input_variables=["text"]
        )
    
    def summarize(self, documents: List[Document], chain_type: str = "map_reduce", callback: Optional[Callable[[float], None]] = None) -> str:
        """生成文档摘要。

        Args:
            documents: 文档列表
            chain_type: 摘要链类型，可选 'map_reduce' 或 'stuff'
            callback: 可选的回调函数，用于更新进度

        Returns:
            生成的摘要文本
        """
        if not documents:
            return "没有提供文档内容，无法生成摘要。"
        
        # 显示进度条
        total_docs = len(documents)
        pbar = tqdm(total=total_docs, desc="生成摘要", ncols=100)
        
        # 自定义回调函数，用于更新进度条
        def progress_callback(progress: float):
            # 更新进度条
            pbar.update(1)
            # 如果提供了外部回调函数，也调用它
            if callback:
                callback(progress)
        
        try:
            # 根据文档长度选择合适的摘要方法
            if chain_type == "map_reduce":
                # 创建map_reduce链，但不立即运行
                chain = load_summarize_chain(
                    self.llm,
                    chain_type="map_reduce",
                    map_prompt=self.map_prompt_template,
                    combine_prompt=self.combine_prompt_template,
                    verbose=False,
                    return_intermediate_steps=True  # 返回中间步骤结果
                )
                
                # 分步处理文档并实时更新进度
                # 首先运行map步骤，为每个文档生成单独的摘要
                map_results = []
                for i, doc in enumerate(documents):
                    # 使用map_prompt处理单个文档
                    result = self.llm.invoke(self.map_prompt_template.format(text=doc.page_content))
                    map_results.append(result.content)
                    
                    # 更新进度（map阶段占总进度的80%）
                    progress = (i + 1) / total_docs * 0.8
                    progress_callback(progress)
                
                # 运行combine步骤，将所有摘要合并
                combined_text = "\n\n".join(map_results)
                final_result = self.llm.invoke(self.combine_prompt_template.format(text=combined_text))
                
                # 更新最终进度（剩余20%）
                progress_callback(1.0)
                
                return final_result.content
            else:  # stuff 方法适用于较短的文档
                chain = load_summarize_chain(
                    self.llm,
                    chain_type="stuff",
                    prompt=self.map_prompt_template,
                    verbose=False
                )
                # 由于stuff方法一次性处理所有文档，直接更新进度到100%
                # result = chain.run(documents)

                contents = "\n".join([doc.page_content for doc in documents])
                result = self.llm.invoke(self.map_prompt_template.format(text=contents))
                progress_callback(1.0)
                return result.content
        except Exception as e:
            logger.error(f"摘要生成失败: {e}")
            return "摘要生成失败，请稍后再试。"
        finally:
            # 确保进度条被关闭
            pbar.close()