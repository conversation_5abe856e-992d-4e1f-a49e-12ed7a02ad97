"""
连接管理器 - 管理HTTP连接的创建、缓存和中断
"""
import asyncio
import weakref
from typing import Dict, Optional
from loguru import logger
import aiohttp
from datetime import datetime


class ConnectionManager:
    """全局连接管理器，用于管理HTTP连接的生命周期和中断"""
    
    def __init__(self):
        # 使用聊天ID作为键，存储连接信息
        self._connections: Dict[str, Dict] = {}
        # 清理锁，防止并发清理
        self._cleanup_lock = asyncio.Lock()
    
    async def create_connection(self, chat_id: str, session: aiohttp.ClientSession, 
                              response: aiohttp.ClientResponse) -> str:
        """
        创建并缓存连接
        
        Args:
            chat_id: 聊天会话ID
            session: aiohttp会话对象
            response: aiohttp响应对象
            
        Returns:
            connection_id: 连接ID
        """
        connection_id = f"{chat_id}_{datetime.now().timestamp()}"
        
        # 使用弱引用避免循环引用导致的内存泄漏
        self._connections[connection_id] = {
            'chat_id': chat_id,
            'session': weakref.ref(session),
            'response': weakref.ref(response),
            'created_at': datetime.now(),
            'is_active': True
        }
        
        logger.info(f"创建连接: {connection_id} for chat: {chat_id}")
        return connection_id
    
    async def abort_connection(self, chat_id: str) -> bool:
        """
        中断指定聊天会话的所有连接
        
        Args:
            chat_id: 聊天会话ID
            
        Returns:
            bool: 是否成功中断
        """
        aborted_count = 0
        connections_to_remove = []
        
        # 先获取连接列表的副本，避免在迭代时修改字典
        connections_snapshot = list(self._connections.items())
        
        for connection_id, conn_info in connections_snapshot:
            if conn_info['chat_id'] == chat_id and conn_info['is_active']:
                try:
                    # 获取弱引用对象
                    session_ref = conn_info['session']()
                    response_ref = conn_info['response']()
                    
                    # 关闭响应连接
                    if response_ref and not response_ref.closed:
                        response_ref.close()
                        logger.info(f"关闭响应连接: {connection_id}")
                    
                    # 关闭会话连接
                    if session_ref and not session_ref.closed:
                        await session_ref.close()
                        logger.info(f"关闭会话连接: {connection_id}")
                    
                    # 标记为非活跃
                    conn_info['is_active'] = False
                    connections_to_remove.append(connection_id)
                    aborted_count += 1
                    
                except Exception as e:
                    logger.error(f"中断连接失败 {connection_id}: {str(e)}")
                    # 即使失败也标记为非活跃
                    conn_info['is_active'] = False
                    connections_to_remove.append(connection_id)
        
        # 清理已中断的连接
        await self._cleanup_connections(connections_to_remove)
        
        if aborted_count > 0:
            logger.info(f"成功中断 {aborted_count} 个连接 for chat: {chat_id}")
            return True
        else:
            logger.warning(f"没有找到活跃的连接 for chat: {chat_id}")
            return False
    
    async def cleanup_connection(self, connection_id: str):
        """
        清理指定连接
        
        Args:
            connection_id: 连接ID
        """
        if connection_id in self._connections:
            conn_info = self._connections[connection_id]
            conn_info['is_active'] = False
            
            # 尝试优雅关闭连接
            try:
                session_ref = conn_info['session']()
                response_ref = conn_info['response']()
                
                if response_ref and not response_ref.closed:
                    response_ref.close()
                
                if session_ref and not session_ref.closed:
                    await session_ref.close()
                    
            except Exception as e:
                logger.error(f"清理连接时出错 {connection_id}: {str(e)}")
            
            # 从缓存中移除
            del self._connections[connection_id]
            logger.info(f"清理连接: {connection_id}")
    
    async def _cleanup_connections(self, connection_ids: list):
        """
        批量清理连接
        
        Args:
            connection_ids: 连接ID列表
        """
        async with self._cleanup_lock:
            for connection_id in connection_ids:
                if connection_id in self._connections:
                    del self._connections[connection_id]
    
    async def cleanup_inactive_connections(self):
        """清理所有非活跃连接"""
        inactive_connections = []
        
        # 先获取连接列表的副本，避免在迭代时修改字典
        connections_snapshot = list(self._connections.items())
        
        for connection_id, conn_info in connections_snapshot:
            if not conn_info['is_active']:
                inactive_connections.append(connection_id)
            else:
                # 检查弱引用是否仍然有效
                session_ref = conn_info['session']()
                response_ref = conn_info['response']()
                
                if session_ref is None or response_ref is None:
                    # 弱引用已失效，标记为非活跃
                    conn_info['is_active'] = False
                    inactive_connections.append(connection_id)
        
        await self._cleanup_connections(inactive_connections)
        
        if inactive_connections:
            logger.info(f"清理了 {len(inactive_connections)} 个非活跃连接")
    
    def get_active_connections_count(self, chat_id: Optional[str] = None) -> int:
        """
        获取活跃连接数量
        
        Args:
            chat_id: 可选，指定聊天会话ID
            
        Returns:
            int: 活跃连接数量
        """
        if chat_id:
            return sum(1 for conn in self._connections.values() 
                      if conn['chat_id'] == chat_id and conn['is_active'])
        else:
            return sum(1 for conn in self._connections.values() if conn['is_active'])
    

# 全局连接管理器实例
connection_manager = ConnectionManager()