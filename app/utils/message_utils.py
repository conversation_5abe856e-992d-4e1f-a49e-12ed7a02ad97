import re
from typing import List
from loguru import logger
from app.models.message import Article


def is_markdown_format(text: str) -> bool:
    """
    检查文本是否包含Markdown格式的图片内容
    
    Args:
        text: 文本字符串
    
    Returns:
        bool: 是否包含Markdown格式的图片
    """
    # 定义正则表达式模式
    patterns = [
        r'^#+\s',  # 标题：#
        r'\*\*.*?\*\*',  # 加粗：**
        r'\[.*?\]\(.+?\)',  # 链接：[这是一个链接](http://work.weixin.qq.com/api/doc)
        r'!\[.*?\]\(.+?\)',  # 图像：![这是一个图像](http://work.weixin.qq.com/api/doc)
        r'`.*?`',  # 行内代码段：`code`
    ]
    
    # 检查是否匹配任何一种模式
    for pattern in patterns:
        if re.search(pattern, text):
            return True
    
    return False

def has_markdown_image(text: str) -> bool:
    """
    检查文本是否包含Markdown格式的图片链接
    
    Args:
        text: 文本字符串
    
    Returns:
        bool: 是否包含Markdown格式的图片链接
    """
    # Markdown图片链接模式 ![alt](url)
    md_image_pattern = r'!\[(.*?)\]\((http[s]?://\S+)\)'
    return bool(re.search(md_image_pattern, text))

def extract_md_imgs(text: str) -> List[dict]:
    """
    从文本中提取Markdown格式的图片链接
    
    Args:
        text: 文本字符串
    
    Returns:
        List[dict]: 包含URL和标题的字典列表
    """
    # Markdown图片链接模式 ![alt](url)
    md_image_pattern = r'!\[(.*?)\]\((http[s]?://\S+)\)'
    urls = []
    for match in re.finditer(md_image_pattern, text):
        alt_text, url = match.groups()
        urls.append({
            'url': url,
            'title': alt_text if alt_text else '图片',
            'is_image': True
        })
    return urls

def extract_urls_from_text(text: str) -> List[dict]:
    """从文本中提取URL和Markdown格式的图片链接
    
    Args:
        text: 文本内容
        
    Returns:
        List[dict]: URL列表，每个URL包含：
            - url: URL地址
            - title: 标题（如果是图片链接则包含alt文本）
            - is_image: 是否是图片链接
    """
    # Markdown图片链接模式 ![alt](url)
    md_image_pattern = r'!\[(.*?)\]\((http[s]?://\S+)\)'
    
    urls = []
    
    # 查找所有Markdown图片链接
    for match in re.finditer(md_image_pattern, text):
        alt_text, url = match.groups()
        urls.append({
            'url': url,
            'title': alt_text if alt_text else '图片',
            'is_image': True,
            'start': match.start(),
            'end': match.end()
        })
    
    # 按照在文本中的位置排序
    urls.sort(key=lambda x: x['start'])
    return urls

def create_articles_from_text(text: str) -> List[Article]:
    """从文本创建图文消息
    
    Args:
        text: 文本内容
        
    Returns:
        List[Article]: 图文消息列表
    """
    # 提取URL和图片链接
    urls = extract_urls_from_text(text)
    
    # 如果没有URL，返回空列表
    if not urls:
        return []
    
    articles = []
    
    # 获取第一个URL之前的所有文本作为正文
    first_url_start = urls[0]['start']
    content_text = text[:first_url_start].strip()
    
    if content_text:  # 如果有正文内容，创建第一篇图文
        try:
            # 提取第一行作为标题
            lines = content_text.splitlines()
            title = lines[0][:50] if lines else "详细信息"
            description = content_text
            
            # 创建正文图文消息
            article = Article(
                title=title,
                description=description,
                url="javascript:void(0);",  # 使用空链接，因为这是纯展示内容
            )
            articles.append(article)
            logger.debug("成功创建正文图文消息: {}", article.dict())
        except Exception as e:
            logger.error("创建正文图文消息失败: {}", str(e))
    
    # 处理URL和图片
    for url_info in urls:
        try:
            url = url_info['url']
            is_image = url_info['is_image']
            
            if is_image:
                # 对于图片，使用alt文本作为标题，或者使用默认标题
                title = url_info['title'] or "查看图片"
                # 使用图片URL作为预览图
                picurl = url
                # 图片链接的描述使用alt文本
                description = url_info['title'] or "点击查看大图"
            else:
                # 从URL中提取域名作为默认标题的一部分
                parsed_url = urlparse(url)
                domain = parsed_url.netloc
                
                # 查找URL前后的上下文（排除已经作为正文使用的部分）
                context_start = max(first_url_start, url_info['start'] - 100)
                context_end = min(len(text), url_info['end'] + 100)
                context = text[context_start:context_end]
                
                # 如果URL前面有换行，可能包含标题
                context_lines = text[context_start:url_info['start']].splitlines()
                title = context_lines[-1].strip() if context_lines else f"查看链接：{domain}"
                if len(title) < 5 or len(title) > 50:
                    title = f"查看链接：{domain}"
                
                # 普通链接不设置预览图
                picurl = None
                description = context.strip()
            
            # 创建图文消息
            article = Article(
                title=title,
                description=description,
                url=url,
                picurl=picurl
            )
            articles.append(article)
            logger.debug("成功创建图文消息: {}", article.dict())
            
        except Exception as e:
            logger.error("创建图文消息失败: {}", str(e))
            continue
    
    return articles

def markdown_to_list_dict(markdown_text):
    """
    将Markdown格式的列表转换为Python的list[dict]格式
    
    Args:
        markdown_text (str): Markdown格式的文本
        
    Returns:
        list[dict]: 包含标题和URL的字典列表
    """
    # 将输入文本按行分割
    lines = markdown_text.strip().split('\n')
    
    # 存储结果的列表
    result = []
    
    # Markdown链接的正则表达式模式
    # 匹配格式: [标题](url)
    pattern = r'\[(.*?)\]\((.*?)\)'
    
    for line in lines:
        # 去除行首的列表标记符号(- 或 * 或 数字.)
        line = line.strip('- *').strip()
        
        # 使用正则表达式查找标题和URL
        match = re.search(pattern, line)
        if match:
            title = match.group(1)
            url = match.group(2)
            
            # 将匹配到的标题和URL添加到结果列表中
            result.append({
                'title': title,
                'url': url
            })
    
    return result

def markdown_to_dict(markdown_data):
    """
    将Markdown格式的键值对列表转换为Python字典。

    :param markdown_data: str, 包含Markdown格式的键值对数据
    :return: dict, 转换后的字典
    """
    # 使用正则表达式提取键值对
    pattern = r"- (.*?)：(.*?)。"
    matches = re.findall(pattern, markdown_data)

    # 将提取的键值对转换为字典
    data_dict = {key.strip(): value.strip() for key, value in matches}

    return data_dict