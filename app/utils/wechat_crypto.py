import time
import socket
import struct
import base64
import hashlib
from typing import <PERSON>ple
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes


class WXBizMsgCrypt:
    def __init__(self, token: str, encoding_aes_key: str, corp_id: str):
        self.token = token
        self.corp_id = corp_id
        self.aes_key = base64.b64decode(encoding_aes_key + "=")

    def verify_url(self, msg_signature: str, timestamp: str, nonce: str, echostr: str) -> str:
        """验证URL函数"""
        signature = self.get_signature(timestamp, nonce, echostr)
        if signature != msg_signature:
            raise ValueError("Invalid signature")
        
        # 解密echostr
        decoded_echostr = self.decrypt(echostr)
        return decoded_echostr

    def get_signature(self, timestamp: str, nonce: str, encrypt: str) -> str:
        """生成签名"""
        sort_list = [self.token, timestamp, nonce, encrypt]
        sort_list.sort()
        sha1 = hashlib.sha1()
        sha1.update("".join(sort_list).encode())
        return sha1.hexdigest()

    def decrypt(self, text: str) -> str:
        """解密函数"""
        # 解密
        iv = self.aes_key[:16]  # 使用密钥的前16字节作为IV
        cipher = Cipher(
            algorithms.AES(self.aes_key),
            modes.CBC(iv),
            backend=default_backend()
        )
        decryptor = cipher.decryptor()
        decrypted_text = decryptor.update(base64.b64decode(text)) + decryptor.finalize()
        
        try:
            pad = decrypted_text[-1]
            content = decrypted_text[16:-pad]
            xml_len = socket.ntohl(struct.unpack("I", content[:4])[0])
            xml_content = content[4:xml_len+4]
            from_corp_id = content[xml_len+4:]
        except Exception as e:
            raise ValueError(f"Decrypt error: {str(e)}")
            
        if from_corp_id.decode() != self.corp_id:
            raise ValueError("Invalid CorpId")
            
        return xml_content.decode()

    def encrypt(self, text: str) -> Tuple[str, str, str]:
        """加密函数"""
        # 生成随机16字节的字符串
        random_str = self.get_random_str()
        
        # 生成xml格式的字符串
        xml_str = text.encode()
        xml_len = socket.htonl(len(xml_str))
        xml_len_str = struct.pack("I", xml_len)
        
        # 拼接明文
        text = random_str + xml_len_str + xml_str + self.corp_id.encode()
        
        # 使用PKCS7补位
        amount_to_pad = 32 - (len(text) % 32)
        if amount_to_pad == 0:
            amount_to_pad = 32
        pad = chr(amount_to_pad).encode() * amount_to_pad
        text = text + pad
        
        # 加密
        iv = self.aes_key[:16]  # 使用密钥的前16字节作为IV
        cipher = Cipher(
            algorithms.AES(self.aes_key),
            modes.CBC(iv),
            backend=default_backend()
        )
        encryptor = cipher.encryptor()
        encrypted = encryptor.update(text) + encryptor.finalize()
        encrypted_str = base64.b64encode(encrypted).decode()
        
        # 生成时间戳和随机字符串
        timestamp = str(int(time.time()))
        nonce = self.get_random_str().decode()
        
        # 生成签名
        signature = self.get_signature(timestamp, nonce, encrypted_str)
        
        return encrypted_str, signature, timestamp

    @staticmethod
    def get_random_str() -> bytes:
        """生成16位随机字符串"""
        return str(int(time.time() * 1000)).encode()[:16]
