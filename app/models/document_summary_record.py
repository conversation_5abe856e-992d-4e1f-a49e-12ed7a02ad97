from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Text, DateTime, func


Base = declarative_base()

class DocumentSummaryRecord(Base):
    """
    文档摘要记录模型
    
    用于存储文档摘要的相关信息，包括原始文件名、摘要内容、摘要状态、进度等
    """
    __tablename__ = 'ai_document_summary_records'

    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(String(64), nullable=False, index=True, comment='摘要任务ID')
    user_id = Column(String(64), nullable=True, comment='用户ID')
    
    # 原始文件信息
    original_filename = Column(String(255), nullable=False, comment='原始文件名')
    original_file_ftp_url = Column(String(512), nullable=True, comment='原始文件FTP地址')
    file_type = Column(String(10), nullable=True, comment='文件类型：pdf, word, ppt')
    
    # 摘要信息
    summary_content = Column(Text, nullable=True, comment='摘要内容')
    summary_progress = Column(Integer, default=0, comment='摘要进度(0-100)')
    summary_status = Column(String(20), default='pending', comment='摘要状态: pending-等待中, processing-处理中, completed-已完成, failed-失败')
    error_message = Column(Text, nullable=True, comment='错误信息')
    
    # 时间信息
    start_time = Column(DateTime, nullable=True, comment='摘要开始时间')
    end_time = Column(DateTime, nullable=True, comment='摘要结束时间')
    process_time = Column(Integer, nullable=True, comment='处理耗时(毫秒)')
    
    # 系统信息
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间') 