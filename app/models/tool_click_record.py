from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, DateTime, func


Base = declarative_base()

class ToolClickRecord(Base):
    __tablename__ = 'tool_click_records'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(64), nullable=False, comment='用户ID')
    tool_name = Column(String(128), nullable=False, comment='工具名称')
    tool_type = Column(String(64), nullable=False, comment='工具类型')
    click_time = Column(DateTime, default=func.now(), comment='点击时间')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
