from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, func


Base = declarative_base()

class MessageRecord(Base):
    __tablename__ = 'wechat_message_records'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(64), nullable=False, comment='企业微信用户ID')
    session_id = Column(String(64), nullable=True, index=True, comment='会话ID')
    session_name = Column(String(128), nullable=True, comment='会话名称')
    message_content = Column(Text, nullable=False, comment='接收到的消息内容')
    response_content = Column(Text, comment='返回的响应内容')
    message_type = Column(String(32), nullable=False, comment='消息类型：text-文本、image-图片、file-文件')
    status = Column(Integer, default=1, comment='处理状态：1-成功，0-失败')
    error_message = Column(Text, comment='错误信息')
    process_time = Column(Integer, comment='处理耗时(毫秒)')
    environment = Column(String(20), nullable=False, default='dev', comment='环境')
    attachment_url = Column(String(512), nullable=True, comment='附件URL')
    is_deleted = Column(Boolean, default=False, nullable=False, comment='是否已删除：0-未删除，1-已删除')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
