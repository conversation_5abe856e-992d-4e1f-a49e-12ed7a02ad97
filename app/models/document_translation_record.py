from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Text, DateTime, func


Base = declarative_base()

class DocumentTranslationRecord(Base):
    """
    文档翻译记录模型
    
    用于存储文档翻译的相关信息，包括原始文件名、翻译后文件名、FTP地址、翻译进度等
    """
    __tablename__ = 'ai_document_translation_records'

    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(String(64), nullable=False, index=True, comment='翻译任务ID')
    user_id = Column(String(64), nullable=True, comment='用户ID')
    
    # 任务类型
    task_type = Column(String(20), default='translation', comment='任务类型: translation-翻译, conversion-转换')
    
    # 原始文件信息
    original_filename = Column(String(255), nullable=False, comment='原始文件名')
    original_file_ftp_url = Column(String(512), nullable=True, comment='原始文件FTP地址')
    
    # 翻译后文件信息
    translated_filename = Column(String(255), nullable=True, comment='翻译后文件名')
    translated_file_ftp_url = Column(String(512), nullable=True, comment='翻译后文件FTP地址')
    
    # 翻译相关信息
    source_language = Column(String(10), nullable=False, comment='源语言代码')
    target_language = Column(String(10), nullable=False, comment='目标语言代码')
    translation_progress = Column(Integer, default=0, comment='翻译进度(0-100)')
    translation_status = Column(String(20), default='pending', comment='翻译状态: pending-等待中, processing-处理中, completed-已完成, failed-失败')
    error_message = Column(Text, nullable=True, comment='错误信息')
    
    # 时间信息
    start_time = Column(DateTime, nullable=True, comment='翻译开始时间')
    end_time = Column(DateTime, nullable=True, comment='翻译结束时间')
    process_time = Column(Integer, nullable=True, comment='处理耗时(毫秒)')
    
    # 系统信息
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
