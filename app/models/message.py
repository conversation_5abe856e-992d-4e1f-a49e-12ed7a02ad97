from pydantic import BaseModel
from typing import Optional


class WeChatMessage(BaseModel):
    """企业微信消息模型"""
    ToUserName: str
    FromUserName: str
    CreateTime: int
    MsgType: str
    Content: Optional[str] = None
    MsgId: str
    Event: Optional[str] = None
    AgentID: str
    PicUrl: Optional[str] = None  # 图片URL
    MediaId: Optional[str] = None  # 图片媒体ID

class Article(BaseModel):
    """图文消息文章"""
    title: str
    description: Optional[str] = None
    url: str  # 改用 str 类型
    picurl: Optional[str] = None  # 改用 str 类型
    btntxt: Optional[str] = None  # 按钮文字，默认为"详情"

    class Config:
        """模型配置"""
        json_encoders = {
            # 如果将来需要自定义序列化
        }

class MengChatResponse(BaseModel):
    """MengChat响应模型"""
    content: str
    message_type: str = "text"
