from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, DateTime, func


Base = declarative_base()

class MessageProcessed(Base):
    """已处理消息记录表"""
    __tablename__ = 'wechat_message_processed'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    msg_id = Column(String(128), nullable=False, unique=True, index=True, comment='消息签名值')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    
    def __repr__(self):
        return f"<MessageProcessed(msg_id='{self.msg_id}')>"
