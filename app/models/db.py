from loguru import logger
from sqlalchemy import create_engine
from contextlib import contextmanager
from sqlalchemy.orm import sessionmaker, Session
from app.core.config import settings


class DatabaseConnection:
    def __init__(self):
        # Get database configuration from settings
        db_user = settings.DB_USER
        db_password = settings.DB_PASSWORD
        db_host = settings.DB_HOST
        db_port = settings.DB_PORT
        db_name = settings.DB_DATABASE
        
        # Create database URL
        self.DATABASE_URL = f"mysql+pymysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        
        # Create engine
        self.engine = create_engine(
            self.DATABASE_URL,
            pool_size=20,        # 连接池大小
            max_overflow=40,     # 最大额外连接数
            pool_timeout=30,     # 等待连接的超时时间（秒）
            pool_pre_ping=True,  # 启用自动重连
            pool_recycle=1800,   # 连接回收时间（秒）
            connect_args={
                'connect_timeout': 5,  # 建立连接的超时时间（秒）
                'read_timeout': 10,    # 读取数据的超时时间（秒）
                'write_timeout': 10    # 写入数据的超时时间（秒）
            },
            echo=False           # 设置为True启用SQL查询日志
        )
        
        # Create session factory
        self.SessionLocal = sessionmaker(
            bind=self.engine,
            # 设置会话超时，超过30秒自动回滚
            expire_on_commit=False
        )
        
        logger.debug("Database connection initialized with host: {}, port: {}, database: {}", 
                   db_host, db_port, db_name)

    def get_pool_status(self):
        """获取连接池状态"""
        if hasattr(self.engine, 'pool'):
            pool = self.engine.pool
            return {
                'size': pool.size(),  # 当前连接数
                'checkedin': pool.checkedin(),  # 可用连接数
                'overflow': pool.overflow(),  # 超出基本连接池的连接数
                'checkedout': pool.checkedout(),  # 正在使用的连接数
            }
        return None

    def log_pool_status(self):
        """记录连接池状态"""
        status = self.get_pool_status()
        if status:
            logger.debug(
                "Database pool status - Size: {}, CheckedIn: {}, Overflow: {}, CheckedOut: {}",
                status['size'],
                status['checkedin'],
                status['overflow'],
                status['checkedout']
            )

    @contextmanager
    def session_marker(self):
        """
        Context manager for database sessions.
        Usage:
            with db.session_marker() as session:
                # do database operations
                session.query(...)
        """
        session: Session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            try:
                session.close()
            except InvalidRequestError:
                print("===> 会话已经关闭。")
            except Exception as e:
                error = str(e)
                print("===> 关闭 session 错误。", error)