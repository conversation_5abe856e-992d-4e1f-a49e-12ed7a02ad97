from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Text, DateTime, func


Base = declarative_base()

class MessageFeedbackRecord(Base):
    __tablename__ = 'message_feedback_records'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(64), nullable=False, comment='企业微信用户ID')
    question = Column(Text, nullable=False, comment='问题')
    answer = Column(Text, comment='回答')
    feedback_type = Column(String(32), nullable=False, comment='反馈类型')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')