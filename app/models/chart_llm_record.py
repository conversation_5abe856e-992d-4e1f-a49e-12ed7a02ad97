from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Text, DateTime, func, JSON


Base = declarative_base()

class ChartLLMRecord(Base):
    """
    图表LLM生成记录模型
    
    用于存储图表LLM生成的相关信息，包括用户查询、提示词、模型参数、生成结果等
    """
    __tablename__ = 'chart_llm_records'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(64), nullable=True, comment='用户ID')
    
    # 请求信息
    query = Column(Text, nullable=False, comment='用户查询内容')
    prompt = Column(Text, nullable=False, comment='完整的提示词')
    model_params = Column(JSON, nullable=True, comment='模型调用参数(JSON格式，包含temperature、max_tokens、model_name等)')
    
    # 响应信息
    result_content = Column(Text, nullable=True, comment='生成的结果内容')
    process_time = Column(Integer, nullable=True, comment='处理耗时(毫秒)')
    status = Column(String(20), default='success', comment='处理状态: success-成功, failed-失败')
    error_message = Column(Text, nullable=True, comment='错误信息')
    
    # 时间信息
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
