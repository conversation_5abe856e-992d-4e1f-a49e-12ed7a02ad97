from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, DateTime, func


Base = declarative_base()

class PushRecord(Base):
    """推送记录模型"""
    __tablename__ = "wechat_push_records"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_ids = Column(String, nullable=False, comment="接收消息的用户ID列表")
    party_ids = Column(String, nullable=True, comment="接收消息的部门ID列表")
    content = Column(String, nullable=False, comment="消息内容")
    msg_type = Column(String, nullable=False, default="text", comment="消息类型")
    status = Column(String, nullable=False, comment="推送状态")
    error_msg = Column(String, nullable=True, comment="错误信息")
    process_time = Column(Integer, nullable=True, comment="处理耗时(毫秒)")
    environment = Column(String(20), nullable=False, default='dev', comment='环境')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
