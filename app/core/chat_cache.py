import time
import asyncio
from threading import Lock
from typing import Dict, List, Optional


class ChatCache:
    """用于存储用户消息的内存缓存类"""
    
    def __init__(self, expiration_time: int = 3600):
        """
        初始化缓存
        
        Args:
            expiration_time: 缓存过期时间(秒)，默认1小时
        """
        self._cache: Dict[str, List[Dict]] = {}  # 用户消息缓存
        self._timestamps: Dict[str, float] = {}  # 缓存时间戳
        self._lock = Lock()  # 线程锁
        self._expiration_time = expiration_time
    
    def add_message(self, user_id: str, message_type: str, content: str) -> None:
        """
        添加消息到缓存
        
        Args:
            user_id: 用户ID
            message_type: 消息类型
            content: 消息内容
        """
        with self._lock:
            if user_id not in self._cache:
                self._cache[user_id] = []
            
            self._cache[user_id].append({
                "type": message_type,
                "content": content
            })
            self._timestamps[user_id] = time.time()
            
    def get_messages(self, user_id: str) -> Optional[List[Dict]]:
        """
        获取用户的消息列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[List[Dict]]: 消息列表，如果用户不存在或缓存过期则返回None
        """
        with self._lock:
            if user_id not in self._cache:
                return None
                
            # 检查是否过期
            if time.time() - self._timestamps[user_id] > self._expiration_time:
                self.clear_user(user_id)
                return None
                
            return self._cache[user_id]
            
    def clear_user(self, user_id: str) -> None:
        """
        清除指定用户的缓存
        
        Args:
            user_id: 用户ID
        """
        with self._lock:
            if user_id in self._cache:
                del self._cache[user_id]
                del self._timestamps[user_id]
                
    def clear_expired(self) -> None:
        """清除所有过期的缓存"""
        current_time = time.time()
        with self._lock:
            expired_users = [
                user_id for user_id, timestamp in self._timestamps.items()
                if current_time - timestamp > self._expiration_time
            ]
            for user_id in expired_users:
                self.clear_user(user_id)

    async def get_last_image_content(self, user_id: str) -> Optional[str]:
        """
        获取用户的最后一条图片消息的内容
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[str]: 最后一条图片消息的内容，如果用户不存在或缓存过期则返回None
        """
        last_messages = self.get_messages(user_id)
        if not last_messages or last_messages[-1]['type'] != 'image':
            return None

        last_image_content = None
        if last_messages:
            # 判断最后一条消息是否为 image 消息
            last_message = last_messages[-1]
            if last_message['type'] == 'image':
                last_image_content = last_message['content']

        # 循环等待，直到最后一条消息类型不是 image 或者内容不是 process
        wait_time = 5
        interval = 1
        elapsed_time = 0
        while elapsed_time < wait_time:
            last_messages = self.get_messages(user_id)
            if last_messages:
                last_message = last_messages[-1]
                if last_message['type'] == 'image' and last_message['content'] == 'process':
                    await asyncio.sleep(interval)
                    elapsed_time += interval
                    continue
                else:
                    last_image_content = last_message['content']
                    break
            break
        return last_image_content

# 创建全局缓存实例
chat_cache = ChatCache()