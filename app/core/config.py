from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    # 企业微信配置
    CORP_ID: str = Field(default="", description="企业微信企业ID")
    CORP_SECRET: str = Field(default="", description="企业微信应用密钥")
    AGENT_ID: str = Field(default="", description="企业微信应用ID")
    TOKEN: str = Field(default="", description="企业微信Token")
    ENCODING_AES_KEY: str = Field(default="", description="企业微信消息加解密密钥")
    FRONTEND_URL: str = Field(default="", description="前端应用URL")
    
    # MengChat配置
    MENGCHAT_API_URL: str = Field(default="", description="MengChat API地址")
    MENGCHAT_API_KEY: str = Field(default="", description="MengChat API密钥")

    # OCR API配置
    OCR_API_URL: str = Field(default="", description="OCR API地址")
    OCR_API_KEY: str = Field(default="", description="OCR API密钥")

    # DeepSeek API配置
    DEEPSEEK_API_URL: str = Field(default="", description="DeepSeek API地址")
    DEEPSEEK_API_KEY: str = Field(default="", description="DeepSeek API密钥")
    DEEPSEEK_API_MODEL: str = Field(default="", description="DeepSeek API模型")

    # 内部模型API配置
    LOCAL_MODEL_API_URL: str = Field(default="", description="内部模型API地址")
    LOCAL_MODEL_API_KEY: str = Field(default="", description="内部模型API密钥")
    LOCAL_MODEL_API_MODEL: str = Field(default="", description="内部模型API模型")

    # 数据库连接配置
    DB_HOST: str = Field(default="", description="数据库主机地址")
    DB_PORT: int = Field(default=3306, description="数据库端口")
    DB_USER: str = Field(default="", description="数据库用户名")
    DB_PASSWORD: str = Field(default="", description="数据库密码")
    DB_DATABASE: str = Field(default="", description="数据库名称")

    # libreoffice 配置
    LIBREOFFICE_NAME: str = Field(default="libreoffice", description="libreoffice 名称")

    # 环境配置
    ENVIRONMENT: str = Field(default="", description="运行环境(dev/prd)")
    SERVER_PORT: int = Field(default=8080, description="服务器端口")

    # LLM平台配置
    LLM_SERVICE_TOKEN_URL: str = Field(default="", description="LLM服务地址")
    LLM_SERVICE_APP_ID: str = Field(default="", description="LLM服务APP ID")
    LLM_SERVICE_APP_SECRET: str = Field(default="", description="LLM服务APP Secret")

    # 远程TTS配置
    TTS_API_URL: str = Field(default="", description="远程TTS服务地址")

    model_config = SettingsConfigDict(
        env_file=".env",
        case_sensitive=True
    )

settings = Settings()