from datetime import datetime
from loguru import logger
from typing import Optional, Dict, Any, List, Tuple
from sqlalchemy.orm import Session

from app.models.db import DatabaseConnection
from app.models.document_summary_record import DocumentSummaryRecord


# 创建数据库连接实例
db = DatabaseConnection()


class DocumentSummaryService:
    """
    文档摘要服务类
    
    提供文档摘要记录的创建、更新、查询等功能
    """
    
    @staticmethod
    def create_summary_record(
        task_id: str,
        original_filename: str,
        user_id: Optional[str] = None,
        file_type: Optional[str] = None
    ) -> int:
        """
        创建文档摘要记录
        
        Args:
            task_id: 摘要任务ID
            original_filename: 原始文件名
            user_id: 用户ID，可选
            file_type: 文件类型，可选，自动从文件扩展名推断
            
        Returns:
            int: 新创建记录的ID
        """
        try:
            # 如果未提供文件类型，从文件名推断
            if not file_type and original_filename:
                if original_filename.lower().endswith(('.pdf')):
                    file_type = 'pdf'
                elif original_filename.lower().endswith(('.docx')):
                    file_type = 'word'
                elif original_filename.lower().endswith(('.pptx')):
                    file_type = 'ppt'
                else:
                    file_type = 'other'
            
            with db.session_marker() as session:
                record = DocumentSummaryRecord(
                    task_id=task_id,
                    user_id=user_id,
                    original_filename=original_filename,
                    file_type=file_type,
                    summary_status="pending",
                    summary_progress=0,
                    start_time=datetime.now()
                )
                session.add(record)
                session.flush()
                record_id = record.id
                logger.info(f"创建文档摘要记录成功，ID: {record_id}, 任务ID: {task_id}")
                return record_id
        except Exception as e:
            logger.error(f"创建文档摘要记录失败: {str(e)}")
            return 0
    
    @staticmethod
    def update_original_file_ftp_url(task_id: str, ftp_url: str) -> bool:
        """
        更新原始文件的FTP URL
        
        Args:
            task_id: 摘要任务ID
            ftp_url: FTP URL
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with db.session_marker() as session:
                record = session.query(DocumentSummaryRecord).filter_by(task_id=task_id).first()
                if record:
                    record.original_file_ftp_url = ftp_url
                    logger.info(f"更新原始文件FTP URL成功，任务ID: {task_id}")
                    return True
                else:
                    logger.warning(f"未找到摘要任务记录，任务ID: {task_id}")
                    return False
        except Exception as e:
            logger.error(f"更新原始文件FTP URL失败: {str(e)}")
            return False
    
    @staticmethod
    def update_summary_progress(
        task_id: str, 
        progress: int, 
        status: Optional[str] = None,
        total_chunks: Optional[int] = None,
        processed_chunks: Optional[int] = None
    ) -> bool:
        """
        更新摘要进度
        
        Args:
            task_id: 摘要任务ID
            progress: 进度值(0-100)
            status: 摘要状态，可选
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with db.session_marker() as session:
                record = session.query(DocumentSummaryRecord).filter_by(task_id=task_id).first()
                if record:
                    record.summary_progress = progress
                    if status:
                        record.summary_status = status
                    logger.info(f"更新摘要进度成功，任务ID: {task_id}, 进度: {progress}%")
                    return True
                else:
                    logger.warning(f"未找到摘要任务记录，任务ID: {task_id}")
                    return False
        except Exception as e:
            logger.error(f"更新摘要进度失败: {str(e)}")
            return False
    
    @staticmethod
    def update_summary_result(
        task_id: str,
        summary_content: str,
        status: str = "completed"
    ) -> bool:
        """
        更新摘要结果
        
        Args:
            task_id: 摘要任务ID
            summary_content: 摘要内容
            status: 摘要状态，默认为completed
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with db.session_marker() as session:
                record = session.query(DocumentSummaryRecord).filter_by(task_id=task_id).first()
                if record:
                    record.summary_content = summary_content
                    record.summary_status = status
                    record.summary_progress = 100
                    record.end_time = datetime.now()
                    
                    # 计算处理耗时(毫秒)
                    if record.start_time:
                        delta = record.end_time - record.start_time
                        record.process_time = int(delta.total_seconds() * 1000)
                    
                    logger.info(f"更新摘要结果成功，任务ID: {task_id}")
                    return True
                else:
                    logger.warning(f"未找到摘要任务记录，任务ID: {task_id}")
                    return False
        except Exception as e:
            logger.error(f"更新摘要结果失败: {str(e)}")
            return False
    
    @staticmethod
    def update_summary_error(task_id: str, error_message: str) -> bool:
        """
        更新摘要错误信息
        
        Args:
            task_id: 摘要任务ID
            error_message: 错误信息
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with db.session_marker() as session:
                record = session.query(DocumentSummaryRecord).filter_by(task_id=task_id).first()
                if record:
                    record.summary_status = "failed"
                    record.error_message = error_message
                    record.end_time = datetime.now()
                    
                    # 计算处理耗时(毫秒)
                    if record.start_time:
                        delta = record.end_time - record.start_time
                        record.process_time = int(delta.total_seconds() * 1000)
                    
                    logger.info(f"更新摘要错误信息成功，任务ID: {task_id}")
                    return True
                else:
                    logger.warning(f"未找到摘要任务记录，任务ID: {task_id}")
                    return False
        except Exception as e:
            logger.error(f"更新摘要错误信息失败: {str(e)}")
            return False
    
    @staticmethod
    def get_summary_record(task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取摘要记录
        
        Args:
            task_id: 摘要任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 摘要记录字典，如果未找到则返回None
        """
        try:
            with db.session_marker() as session:
                record = session.query(DocumentSummaryRecord).filter_by(task_id=task_id).first()
                if record:
                    return {
                        "id": record.id,
                        "task_id": record.task_id,
                        "user_id": record.user_id,
                        "original_filename": record.original_filename,
                        "original_file_ftp_url": record.original_file_ftp_url,
                        "file_type": record.file_type,
                        "summary_content": record.summary_content,
                        "summary_progress": record.summary_progress,
                        "summary_status": record.summary_status,
                        "start_time": record.start_time.strftime("%Y-%m-%d %H:%M:%S") if record.start_time else None,
                        "end_time": record.end_time.strftime("%Y-%m-%d %H:%M:%S") if record.end_time else None,
                        "process_time": record.process_time,
                        "created_at": record.created_at.strftime("%Y-%m-%d %H:%M:%S") if record.created_at else None,
                        "updated_at": record.updated_at.strftime("%Y-%m-%d %H:%M:%S") if record.updated_at else None
                    }
                else:
                    logger.warning(f"未找到摘要任务记录，任务ID: {task_id}")
                    return None
        except Exception as e:
            logger.error(f"获取摘要记录失败: {str(e)}")
            return None
    
    @staticmethod
    def get_summary_records(
        user_id: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 10,
        offset: int = 0
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取摘要记录列表
        
        Args:
            user_id: 用户ID，可选，如果提供则过滤指定用户的记录
            status: 摘要状态，可选，如果提供则过滤指定状态的记录
            limit: 每页记录数，默认为10
            offset: 偏移量，默认为0
            
        Returns:
            Tuple[List[Dict[str, Any]], int]: 记录列表和总记录数
        """
        try:
            with db.session_marker() as session:
                query = session.query(DocumentSummaryRecord)
                
                # 应用过滤条件
                if user_id:
                    query = query.filter(DocumentSummaryRecord.user_id == user_id)
                if status:
                    query = query.filter(DocumentSummaryRecord.summary_status == status)
                
                # 获取总记录数
                total_count = query.count()
                
                # 应用分页
                records = query.order_by(DocumentSummaryRecord.created_at.desc()).limit(limit).offset(offset).all()
                
                # 转换为字典列表
                result = []
                for record in records:
                    result.append({
                        "id": record.id,
                        "task_id": record.task_id,
                        "user_id": record.user_id,
                        "original_filename": record.original_filename,
                        "file_type": record.file_type,
                        "summary_content": record.summary_content,
                        "summary_progress": record.summary_progress,
                        "summary_status": record.summary_status,
                        "start_time": record.start_time.strftime("%Y-%m-%d %H:%M:%S") if record.start_time else None,
                        "end_time": record.end_time.strftime("%Y-%m-%d %H:%M:%S") if record.end_time else None,
                        "process_time": record.process_time,
                        "created_at": record.created_at.strftime("%Y-%m-%d %H:%M:%S") if record.created_at else None
                    })
                
                return result, total_count
        except Exception as e:
            logger.error(f"获取摘要记录列表失败: {str(e)}")
            return [], 0 