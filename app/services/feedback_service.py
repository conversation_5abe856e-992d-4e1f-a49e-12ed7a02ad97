from loguru import logger
from datetime import datetime
from app.models.db import DatabaseConnection
from app.models.message_feedback_record import MessageFeedbackRecord


db = DatabaseConnection()

def record_feedback_message(
    user_id: str,
    question: str,
    answer: str,
    feedback_type: str,
) -> None:
    """记录反馈消息到数据库
    
    Args:
        user_id: 用户ID
        question: 用户发送的问题
        answer: 系统的回复内容
        feedback_type: 反馈类型
    """
    try:
        db.log_pool_status()  # 记录操作前的连接池状态
        with db.session_marker() as session:
            record = MessageFeedbackRecord(
                user_id=user_id,
                question=question,
                answer=answer,
                feedback_type=feedback_type,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(record)
            session.commit()
            logger.info("成功记录消息到数据库")
    except Exception as e:
        logger.error("记录消息到数据库失败: {}", str(e))
        raise
    finally:
        db.log_pool_status()  # 记录操作后的连接池状态
