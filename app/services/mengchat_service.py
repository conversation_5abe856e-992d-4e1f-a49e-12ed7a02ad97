import aiohttp
from loguru import logger
from typing import Dict, Any
from app.core.config import settings
from app.models.message import MengChatResponse



class MengChatService:
    def __init__(self):
        self.api_url = settings.MENGCHAT_API_URL
        self.api_key = settings.MENGCHAT_API_KEY

    async def process_message(self, userId: str, message: Dict[str, Any]) -> MengChatResponse:
        """处理消息并获取回复
        
        Args:
            userId: 用户的userId
            message: 用户发送的消息
            
        Returns:
            MengChatResponse: MengChat的响应
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        data = {
            "chatId": userId,
            "stream": False,
            "detail": False,
            "messages": [message],
            "variables": {'userId': userId}
        }
        
        try:
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=600)  # 60秒超时
            ) as session:
                logger.debug("Sending request to MengChat API: {}", data)
                
                async with session.post(
                    f"{self.api_url}",
                    headers=headers,
                    json=data,
                    raise_for_status=True,
                    timeout=aiohttp.ClientTimeout(total=600)
                ) as response:
                    result = await response.json()
                    
                    # 解析响应
                    content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                    content = content.strip()
                    logger.debug("Received response from MengChat API: {}", content)
                    return MengChatResponse(content=content)
        except Exception as e:
            logger.error("Unexpected error in process_message: {}", str(e))
            return MengChatResponse(content="服务器繁忙，请稍后再试。")
