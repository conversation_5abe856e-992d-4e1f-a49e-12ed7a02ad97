from loguru import logger
from typing import List, Dict, Any
from datetime import datetime

from app.models.db import DatabaseConnection
from app.models.tool_click_record import ToolClickRecord


db = DatabaseConnection()

def record_tool_click(
    user_id: str,
    tool_name: str,
    tool_type: str,
    click_time: datetime = None
) -> int:
    """记录用户工具点击信息到数据库

    Args:
        user_id: 用户ID
        tool_name: 工具名称
        tool_type: 工具类型
        click_time: 点击时间，如果为None则使用当前时间

    Returns:
        int: 记录ID
    """
    try:
        db.log_pool_status()  # 记录操作前的连接池状态

        with db.session_marker() as session:
            record = ToolClickRecord(
                user_id=user_id,
                tool_name=tool_name,
                tool_type=tool_type,
                click_time=click_time or datetime.now()
            )
            session.add(record)
            session.commit()
            logger.info(f"成功记录工具点击信息: 用户 {user_id} 点击了 {tool_name}")
            return record.id
    except Exception as e:
        logger.error(f"记录工具点击信息失败: {str(e)}")
        raise
    finally:
        db.log_pool_status()  # 记录操作后的连接池状态


def get_user_tool_clicks(user_id: str) -> List[Dict[str, Any]]:
    """获取用户的工具点击记录

    Args:
        user_id: 用户ID

    Returns:
        List[Dict[str, Any]]: 用户的工具点击记录列表
    """
    try:
        db.log_pool_status()  # 记录操作前的连接池状态

        with db.session_marker() as session:
            records = session.query(ToolClickRecord).filter(
                ToolClickRecord.user_id == user_id
            ).order_by(
                ToolClickRecord.click_time.desc()
            ).all()

            result = []
            for record in records:
                result.append({
                    "id": record.id,
                    "user_id": record.user_id,
                    "tool_name": record.tool_name,
                    "tool_type": record.tool_type,
                    "click_time": record.click_time,
                    "created_at": record.created_at
                })
            
            logger.info(f"成功获取用户 {user_id} 的工具点击记录，共 {len(result)} 条")
            return result
    except Exception as e:
        logger.error(f"获取用户工具点击记录失败: {str(e)}")
        raise
    finally:
        db.log_pool_status()  # 记录操作后的连接池状态


def get_tool_click_stats(tool_type: str = None) -> List[Dict[str, Any]]:
    """获取工具点击统计数据

    Args:
        tool_type: 工具类型，如果为None则统计所有类型

    Returns:
        List[Dict[str, Any]]: 工具点击统计数据
    """
    try:
        db.log_pool_status()  # 记录操作前的连接池状态

        from sqlalchemy import func

        with db.session_marker() as session:
            query = session.query(
                ToolClickRecord.tool_name,
                ToolClickRecord.tool_type,
                func.count(ToolClickRecord.id).label('click_count')
            ).group_by(
                ToolClickRecord.tool_name,
                ToolClickRecord.tool_type
            )
            
            if tool_type:
                query = query.filter(ToolClickRecord.tool_type == tool_type)
                
            records = query.order_by(func.count(ToolClickRecord.id).desc()).all()

            result = []
            for record in records:
                result.append({
                    "tool_name": record.tool_name,
                    "tool_type": record.tool_type,
                    "click_count": record.click_count
                })
            
            logger.info(f"成功获取工具点击统计数据，共 {len(result)} 条")
            return result
    except Exception as e:
        logger.error(f"获取工具点击统计数据失败: {str(e)}")
        raise
    finally:
        db.log_pool_status()  # 记录操作后的连接池状态
