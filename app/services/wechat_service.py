import re
import os
import time
import uuid
import requests
import mimetypes
from loguru import logger
from datetime import datetime
from urllib.parse import quote
import xml.etree.ElementTree as ET
from typing import Dict, Any, List, Union

from app.core.config import settings
from app.models.message import WeChatMessage, Article
from app.utils.message_utils import markdown_to_list_dict, markdown_to_dict


class WeChatService:
    def __init__(self):
        self.corp_id = settings.CORP_ID
        self.corp_secret = settings.CORP_SECRET
        self.agent_id = settings.AGENT_ID
        self._access_token = None
        self._token_expires_at = 0  # Token过期时间戳
        self._jsapi_ticket = None
        self._ticket_expires_at = 0 # Ticket过期时间戳

    def get_access_token(self) -> str:
        """获取访问令牌，如果令牌过期会自动重新获取
        
        Returns:
            str: 访问令牌
            
        Raises:
            ValueError: 获取令牌失败时抛出
        """
        current_time = int(time.time())
        
        # 如果token不存在或者已经过期（预留10分钟缓冲时间），重新获取
        if not self._access_token or current_time >= (self._token_expires_at - 600):
            url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken"
            params = {
                "corpid": self.corp_id,
                "corpsecret": self.corp_secret
            }
            
            try:
                response = requests.get(url, params=params)
                result = response.json()
                
                if result.get("errcode") != 0:
                    raise ValueError(f"Failed to get access token: {result.get('errmsg')}")
                
                self._access_token = result.get("access_token")
                # 设置过期时间（access_token默认7200秒后过期）
                self._token_expires_at = current_time + 7200
                logger.debug("获取新的access_token成功，将在{}过期", 
                           time.strftime('%Y-%m-%d %H:%M:%S', 
                                       time.localtime(self._token_expires_at)))
            except Exception as e:
                logger.error("获取access_token失败: {}", str(e))
                raise ValueError(f"Failed to get access token: {str(e)}")
        
        return self._access_token

    def get_ticket(self):
        """
        获取JS-SDK的ticket
        """
        current_time = int(time.time())
        
        # 如果token不存在或者已经过期（预留10分钟缓冲时间），重新获取
        if not self._jsapi_ticket or current_time >= (self._ticket_expires_at - 600):
            try:
                url = f"https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token={self.get_access_token()}"
                response = requests.get(url)
                result = response.json()

                if result.get("errcode") != 0:
                    raise ValueError(f"Failed to get ticket: {result.get('errmsg')}")

                self._jsapi_ticket = result.get("ticket")
                self._ticket_expires_at = current_time + 7200
                logger.debug("获取新的ticket成功，将在{}过期", 
                           time.strftime('%Y-%m-%d %H:%M:%S', 
                                       time.localtime(self._ticket_expires_at)))

            except Exception as e:
                logger.error("获取ticket失败: {}", str(e))
                raise ValueError(f"Failed to get ticket: {str(e)}")

        return self._jsapi_ticket
    
    def get_wx_config(self):
        """
        获取JS-SDK的配置信息
        """
        return {
            "debug": False,
            "appId": self.corp_id,
            "timestamp": int(time.time()),
            "nonceStr": str(uuid.uuid4()),
            "signature": self.get_ticket(),
        }

    def get_signature(self, timestamp: int, nonce: str, url: str) -> str:
        """
        使用SHA1算法生成签名
        1. 对所有参数按照字段名进行字典序排序
        2. 使用URL键值对的格式拼接成字符串
        3. 使用SHA1进行加密，获取签名
        """
        import hashlib

        # 构建参数字典
        params = {
            "jsapi_ticket": self.get_ticket(),
            "noncestr": nonce,
            "timestamp": str(timestamp),
            "url": url
        }
        print('params: ', params)
        
        # 按照字典序排序
        sorted_params = sorted(params.items(), key=lambda x: x[0])
        
        # 拼接成字符串
        string_to_sign = "&".join(["=".join(item) for item in sorted_params])
        
        # 使用SHA1进行加密
        signature = hashlib.sha1(string_to_sign.encode('utf-8')).hexdigest()
        print('signature: ', signature.strip())
        return signature

    def parse_message(self, xml_content: str) -> WeChatMessage:
        """解析企业微信消息
        
        Args:
            xml_content: XML格式的消息内容
            
        Returns:
            WeChatMessage: 解析后的消息对象
        """
        try:
            logger.debug("开始解析XML消息")
            # 使用ET解析XML
            root = ET.fromstring(xml_content)
            
            # 提取所有字段
            msg_dict = {}
            for child in root:
                msg_dict[child.tag] = child.text
            
            # 处理CreateTime字段，确保它是整数
            if 'CreateTime' in msg_dict:
                msg_dict['CreateTime'] = int(msg_dict['CreateTime'])
            
            # 设置默认值
            msg_dict.setdefault('Content', '')  # 为非文本消息设置默认值
            msg_dict.setdefault('AgentID', self.agent_id)  # 使用当前应用的AgentID
            
            # 生成默认的MsgId（如果没有）
            import time
            msg_dict.setdefault('MsgId', str(int(time.time() * 1000)))
            logger.debug("XML消息解析完成，消息字典：{}", msg_dict)
            
            # 将字典转换为WeChatMessage
            return WeChatMessage(**msg_dict)
            
        except ET.ParseError as e:
            raise ValueError(f"Invalid XML format: {str(e)}")
        except Exception as e:
            raise ValueError(f"Failed to parse message: {str(e)}")

    def upload_media(self, file_path: str, media_type: str = "image") -> str:
        """上传临时媒体文件
        
        Args:
            file_path: 文件路径
            media_type: 媒体文件类型，可选值：image、voice、video、file
            
        Returns:
            str: 媒体文件ID
        """
        if not os.path.exists(file_path):
            raise ValueError(f"File not found: {file_path}")
            
        url = f"https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token={self.get_access_token()}&type={media_type}"
        
        # 获取文件MIME类型
        mime_type = mimetypes.guess_type(file_path)[0]
        if not mime_type:
            mime_type = 'application/octet-stream'
            
        with open(file_path, 'rb') as f:
            files = {
                'media': (
                    os.path.basename(file_path),
                    f,
                    mime_type
                )
            }
            response = requests.post(url, files=files)
            
        result = response.json()
        if result.get("errcode") != 0:
            raise ValueError(f"Failed to upload media: {result.get('errmsg')}")
            
        return result.get("media_id")

    def upload_media_from_url(self, url: str) -> str:
        """从URL上传媒体文件到企业微信
    
        Args:
            url: 媒体文件的URL地址
        
        Returns:
            str: 媒体文件ID
        """
        # 下载文件内容
        response = requests.get(url, stream=True)
        if response.status_code != 200:
            raise ValueError(f"Failed to download file from URL: {url}")
        
        # 从URL中获取文件名
        filename = url.split('/')[-1]
        if not filename:
            filename = 'file'
        
        # 获取文件MIME类型
        content_type = response.headers.get('content-type', 'application/octet-stream')
        
        # 构建上传URL
        upload_url = f"https://qyapi.weixin.qq.com/cgi-bin/media/upload"
        params = {
            "access_token": self.get_access_token(),
            "type": "file"  # 默认为file类型，可以根据content_type来判断具体类型
        }
        
        # 根据content_type判断媒体类型
        if content_type.startswith('image/'):
            params['type'] = 'image'
        elif content_type.startswith('video/'):
            params['type'] = 'video'
        elif content_type.startswith('audio/'):
            params['type'] = 'voice'
        
        # 准备文件数据
        files = {
            'media': (
                filename,
                response.content,
                content_type
            )
        }
        
        # 上传到企业微信
        upload_response = requests.post(upload_url, params=params, files=files)
        result = upload_response.json()
        
        if result.get("errcode") != 0:
            raise ValueError(f"Failed to upload media: {result.get('errmsg')}")
        
        return result.get("media_id")

    def send_message(
        self,
        user_id: str,
        content: Union[str, List[Article], Dict[str, str]],
        party_id: str = None,
        msg_type: str = "text"
    ) -> Dict[str, Any]:
        """发送消息到企业微信
        
        Args:
            user_id: 接收消息的用户ID
            party_id: 接收消息的部门ID
            content: 消息内容，根据msg_type不同而不同：
                    - text: 字符串
                    - image: 媒体文件ID
                    - news: Article列表
                    - mpnews: 图文消息，包含media_id
            msg_type: 消息类型，可选值：text、image、news、mpnews
            
        Returns:
            Dict[str, Any]: 发送结果
        """
        result = None
        if msg_type == "text" or msg_type == "markdown":
            # 发送文本类型消息
            result = self.send_text_message(user_id, party_id, content, msg_type)
        elif msg_type == "image" or msg_type == "news" or msg_type == "mpnews":
            # 发送媒体类型消息
            result = self.send_media_message(user_id, party_id, content, msg_type)
        elif msg_type == "process":
            # 发送流程消息
            result = self.send_process_message(user_id, party_id, content, msg_type)
        elif msg_type == "mermaid":
            # 发送Mermaid图消息
            result = self.send_mermaid_message(user_id, party_id, content, "image")
        elif msg_type == "excel":
            # 发送Excel文件消息
            result = self.send_excel_message(user_id, party_id, content, "file")
        else:
            raise ValueError(f"Invalid message type: {msg_type}")
        return {"code": 0, "data": result}

    def send_text_message(self, user_id: str, party_id: str, content: Union[str, List[Article], Dict[str, str]], msg_type: str = ""):
        url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={self.get_access_token()}"
        
        data = {
            "msgtype": msg_type,
            "agentid": self.agent_id,
            "safe": 0  # 是否是保密消息
        }
        if party_id is not None and party_id != "":
            data["toparty"] = party_id
        if user_id is not None and user_id != "":
            data["touser"] = user_id

        # 判断消息长度，当超过2048字节时，分批次发送消息
        contents = []
        if len(bytes(content, encoding='utf-8')) > 2048:
            strs = split_string_by_byte_length(content, 2048)
            contents = [str.decode('utf-8') for str in strs]
        else:
            # Content为空，回复兜底消息
            if not content or content == "":
                content = "这个问题小盟不知道如何回答，请换个问题再试试吧！"
            contents = [content]


        # 根据消息类型设置不同的消息内容
        result = None
        for content in contents:
            if msg_type == "text":
                data["text"] = {"content": content}
            elif msg_type == "markdown":
                if isinstance(content, str):
                    # 替换MD格式图片为MD格式的链接
                    content = replace_image_to_link(content)
                    data["markdown"] = {"content": content}
                else:
                    break
            
            # 发送消息
            logger.debug("Sending message to WeChat: {}", data)
            response = requests.post(url, json=data)
            result = response.json()

            # 如果是分批次发送，需要等待200毫秒，否则到达企微消息可能顺序错乱
            if len(contents) > 1:
                time.sleep(0.20)
            
            if result.get("errcode") != 0:
                if "user & party & tag all invalid" in result.get('errmsg'):
                    return "对话失败，您无权限访问MengChat办公助手，如需继续使用请联系管理员！"
                raise ValueError(f"Failed to send message: {result.get('errmsg')}")
        return result

    def send_process_message(self, user_id: str, party_id: str, content: Union[str, List[Article], Dict[str, str]], msg_type: str = ""):
        """处理流程类型消息"""
        # 代办/已办/详情处理
        articles = []
        if '最近的待办任务' in content:
            todo_list = markdown_to_list_dict(content)
            todo_count = todo_list[0]['url'].split('count=')[1]
            todo_text = ""
            for todo in todo_list[:3]:
                if '查看更多' not in todo['title']:
                    todo_text += f"• {todo['title']}\n"
            articles.append(
                {
                    "title": "待办任务",
                    "description": f"已查询到您有{todo_count}个待办任务，下面是部分任务：\n{todo_text}  ...\n点击查看并处理",
                    "url": f"http://mobileoa.groupama-sdig.com/x6/wxproxy?redirect={quote('http://mobileoa.groupama-sdig.com/x6/mobile/view/bpm/myMatters.html')}",
                }
            )
        elif '最近的已办任务' in content:
            done_list = markdown_to_list_dict(content)
            done_count = done_list[0]['url'].split('count=')[1]
            done_text = ""
            for done in done_list[:3]:
                if '查看更多' not in done['title']:
                    done_text += f"• {done['title']}\n"
            articles.append(
                {
                    "title": "已办任务",
                    "description": f"已查询到您最近办理了{done_count}个任务，下面是部分任务：\n{done_text}  ...\n点击查看",
                    "url": f"http://mobileoa.groupama-sdig.com/x6/wxproxy?redirect={quote('http://mobileoa.groupama-sdig.com/x6/mobile/view/bpm/myMatters.html?showStatus=already')}",
                }
            )
        elif '流程的最新状态' in content:
            data_dict = markdown_to_dict(content)
            pattern = r"\[.*?\]\((.*?)\)"
            matches = re.findall(pattern, data_dict['流转详情'])
            run_id = matches[0].split('procInstId=')[1]
            articles.append(
                {
                    "title": "流程最新状态",
                    "description": f"""• 流程标题：{data_dict['流程标题']}
• 创 建 人 ：{data_dict['创建人']}
• 当前状态：{data_dict['当前状态']}
• 当前节点：{data_dict['当前节点']}
• 流转时间：{data_dict['流转时间']}""",
                    "url": f"http://mobileoa.groupama-sdig.com/x6/wxproxy?redirect={quote(f'http://mobileoa.groupama-sdig.com/x6/mobile/view/bpm/getProcessRun.html?runId={run_id}')}",
                }
            )
        return self.send_media_message(user_id=user_id, party_id=party_id, content=articles, msg_type="news")


    def send_media_message(self, user_id: str, party_id: str, content: Union[str, List[Article], Dict[str, str]], msg_type: str = ""):
        url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={self.get_access_token()}"
        
        data = {
            "touser": user_id,
            "msgtype": msg_type,
            "agentid": self.agent_id,
            "safe": 0  # 是否是保密消息
        }
        if party_id is not None:
            data["toparty"] = party_id

        if msg_type == "image":
            if isinstance(content, str):
                data["image"] = {"media_id": content}
            else:
                raise ValueError("Image message requires media_id as content")
        elif msg_type == "news":
            if isinstance(content, list):
                data["news"] = {
                    "articles": content
                }
            else:
                raise ValueError("News message requires a list of Article objects")
        elif msg_type == "mpnews":
            if isinstance(content, list):
                article = content[0]
                if not isinstance(article, dict):
                    raise ValueError("Mpnews message requires a list of dict objects")
                    
                try:
                    if "picurl" in article and article["picurl"].startswith("http"):
                        article["thumb_media_id"] = self.upload_media_from_url(article["picurl"])
                except Exception as e:
                    logger.error("Failed to upload thumb media: {}", str(e))
                    raise ValueError(f"Failed to upload thumb media: {str(e)}")
                    
                data["mpnews"] = {"articles": [article]}
            else:
                raise ValueError("Mpnews message requires a list with at least one article")
        
        # 发送消息
        logger.debug("Sending message to WeChat: {}", data)
        response = requests.post(url, json=data)
        result = response.json()
        
        if result.get("errcode") != 0:
            if "user & party & tag all invalid" in result.get('errmsg'):
                return "对话失败，您无权限访问MengChat办公助手，如需继续使用请联系管理员！"
            raise ValueError(f"Failed to send message: {result.get('errmsg')}")
        return result

    def send_mermaid_message(self, user_id: str, party_id: str, content: str, msg_type: str = ""):
        temp_file = ''
        try:
            mermaid_code_block = content.replace('```mermaid', '').replace('```', '')

            # 调用接口下载图片
            print(mermaid_code_block)
            print('http://10.255.74.119:8888/render-mermaid?mermaid_code=', quote(mermaid_code_block))
            response = requests.get('http://10.255.74.119:8888/render-mermaid', params={"mermaid_code": quote(mermaid_code_block)})
            temp_file = f'./mermaid-{str(uuid.uuid4())}.png'
            if response.status_code == 200:
                with open(temp_file, 'wb') as f:
                    f.write(response.content)

            media_id = self.upload_media(temp_file)
            
            # 发送Mermaid图消息
            url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={self.get_access_token()}"
            
            data = {
                "touser": user_id,
                "msgtype": msg_type,
                "agentid": self.agent_id,
                "safe": 0  # 是否是保密消息
            }
            if party_id is not None:
                data["toparty"] = party_id

            data["image"] = {"media_id": media_id}
            
            # 发送消息
            logger.debug("Sending message to WeChat: {}", data)
            response = requests.post(url, json=data)
            result = response.json()
        
        except Exception as e:
            if "user & party & tag all invalid" in result.get('errmsg'):
                return "对话失败，您无权限访问MengChat办公助手，如需继续使用请联系管理员！"
            raise ValueError(f"Failed to send message: {str(e)}")
        finally:
            if temp_file != '':
                os.remove(temp_file)
        
        if result.get("errcode") != 0:
            if "user & party & tag all invalid" in result.get('errmsg'):
                return "对话失败，您无权限访问MengChat办公助手，如需继续使用请联系管理员！"
            raise ValueError(f"Failed to send message: {result.get('errmsg')}")
        return result
    
    def send_excel_message(self, user_id: str, party_id: str, content: str, msg_type: str = ""):
        url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={self.get_access_token()}"

        data = {
            "touser": user_id,
            "msgtype": msg_type,
            "agentid": self.agent_id,
        }
        if party_id is not None and party_id != "":
            data["toparty"] = party_id

        temp_file = ''
        try:
            response = requests.get(content)
            if os.path.exists('./temp/downloads'):
                temp_file = f'./temp/downloads/每日缴费快报通报-{datetime.now().strftime("%Y-%m-%d")}.xlsx'
            else:
                os.makedirs('./temp/downloads')
                temp_file = f'./temp/downloads/每日缴费快报通报-{datetime.now().strftime("%Y-%m-%d")}.xlsx'
            if response.status_code == 200:
                with open(temp_file, 'wb') as f:
                    f.write(response.content)
            media_id = self.upload_media(temp_file, msg_type)

            data["file"] = {"media_id": media_id}

            # 发送消息
            logger.debug("Sending message to WeChat: {}", data) 
            response = requests.post(url, json=data)
            result = response.json()

            if result.get("errcode") != 0:
                if "user & party & tag all invalid" in result.get('errmsg'):
                    return "对话失败，您无权限访问MengChat办公助手，如需继续使用请联系管理员！"
                raise ValueError(f"Failed to send message: {result.get('errmsg')}")
            return result
        finally:
            if temp_file != '':
                os.remove(temp_file)

def replace_image_to_link(text):
    # 定义正则表达式模式，匹配Markdown格式的图片
    pattern = r'!\[(.*?)\]\((.*?)\)'
    
    # 使用re.sub进行替换
    result = re.sub(pattern, r'[点击查看](\2)', text)
    
    return result

def split_string_by_byte_length(s, max_byte_length=2048):
    """
    将字符串按字节长度分割成多个子字符串，每个子字符串的字节长度不超过 max_byte_length。
    """
    str_bytes = bytes(s, encoding='utf-8')
    result = []
    start = 0
    while start < len(str_bytes):
        # 计算当前段的结束位置
        end = start + max_byte_length
        # 如果结束位置超过总长度，直接取到最后
        if end >= len(str_bytes):
            result.append(str_bytes[start:])
            break
        # 检查是否截断了多字节字符
        while end > start and (str_bytes[end] & 0b11000000) == 0b10000000:
            end -= 1
        # 如果 end 和 start 相同，说明无法分割，直接取到最后
        if end == start:
            end = start + max_byte_length
        result.append(str_bytes[start:end])
        start = end
    return result