from datetime import datetime
from loguru import logger
from typing import Optional, Dict, Any, List, Tuple
from sqlalchemy.orm import Session

from app.models.db import DatabaseConnection
from app.models.document_translation_record import DocumentTranslationRecord


# 创建数据库连接实例
db = DatabaseConnection()


class DocumentTranslationService:
    """
    文档翻译服务类
    
    提供文档翻译记录的创建、更新、查询等功能
    """
    
    @staticmethod
    def create_translation_record(
        task_id: str,
        original_filename: str,
        source_language: str,
        target_language: str,
        user_id: Optional[str] = None,
        task_type: str = "translation"
    ) -> int:
        """
        创建文档翻译记录
        
        Args:
            task_id: 翻译任务ID
            original_filename: 原始文件名
            source_language: 源语言代码
            target_language: 目标语言代码
            user_id: 用户ID，可选
            task_type: 任务类型，可选，默认为"translation"，可选值："translation"-翻译, "conversion"-转换
            
        Returns:
            int: 新创建记录的ID
        """
        try:
            with db.session_marker() as session:
                record = DocumentTranslationRecord(
                    task_id=task_id,
                    user_id=user_id,
                    task_type=task_type,
                    original_filename=original_filename,
                    source_language=source_language,
                    target_language=target_language,
                    translation_status="pending",
                    translation_progress=0,
                    start_time=datetime.now()
                )
                session.add(record)
                session.flush()
                record_id = record.id
                logger.info(f"创建文档翻译记录成功，ID: {record_id}, 任务ID: {task_id}, 任务类型: {task_type}")
                return record_id
        except Exception as e:
            logger.error(f"创建文档翻译记录失败: {str(e)}")
            return 0
    
    @staticmethod
    def update_original_file_ftp_url(task_id: str, ftp_url: str) -> bool:
        """
        更新原始文件的FTP URL
        
        Args:
            task_id: 翻译任务ID
            ftp_url: FTP URL
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with db.session_marker() as session:
                record = session.query(DocumentTranslationRecord).filter_by(task_id=task_id).first()
                if record:
                    record.original_file_ftp_url = ftp_url
                    logger.info(f"更新原始文件FTP URL成功，任务ID: {task_id}")
                    return True
                else:
                    logger.warning(f"未找到翻译任务记录，任务ID: {task_id}")
                    return False
        except Exception as e:
            logger.error(f"更新原始文件FTP URL失败: {str(e)}")
            return False
    
    @staticmethod
    def update_translation_progress(task_id: str, progress: int, status: str = None) -> bool:
        """
        更新翻译进度
        
        Args:
            task_id: 翻译任务ID
            progress: 进度值(0-100)
            status: 翻译状态，可选
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with db.session_marker() as session:
                record = session.query(DocumentTranslationRecord).filter_by(task_id=task_id).first()
                if record:
                    record.translation_progress = progress
                    if status:
                        record.translation_status = status
                    logger.info(f"更新翻译进度成功，任务ID: {task_id}, 进度: {progress}%")
                    return True
                else:
                    logger.warning(f"未找到翻译任务记录，任务ID: {task_id}")
                    return False
        except Exception as e:
            logger.error(f"更新翻译进度失败: {str(e)}")
            return False
    
    @staticmethod
    def update_translation_result(
        task_id: str,
        translated_filename: str,
        translated_file_ftp_url: Optional[str] = None,
        status: str = "completed"
    ) -> bool:
        """
        更新翻译结果
        
        Args:
            task_id: 翻译任务ID
            translated_filename: 翻译后文件名
            translated_file_ftp_url: 翻译后文件FTP URL，可选
            status: 翻译状态，默认为completed
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with db.session_marker() as session:
                record = session.query(DocumentTranslationRecord).filter_by(task_id=task_id).first()
                if record:
                    record.translated_filename = translated_filename
                    if translated_file_ftp_url:
                        record.translated_file_ftp_url = translated_file_ftp_url
                    record.translation_status = status
                    record.translation_progress = 100
                    record.end_time = datetime.now()
                    
                    # 计算处理耗时(毫秒)
                    if record.start_time:
                        delta = record.end_time - record.start_time
                        record.process_time = int(delta.total_seconds() * 1000)
                    
                    logger.info(f"更新翻译结果成功，任务ID: {task_id}")
                    return True
                else:
                    logger.warning(f"未找到翻译任务记录，任务ID: {task_id}")
                    return False
        except Exception as e:
            logger.error(f"更新翻译结果失败: {str(e)}")
            return False
    
    @staticmethod
    def update_translation_error(task_id: str, error_message: str) -> bool:
        """
        更新翻译错误信息
        
        Args:
            task_id: 翻译任务ID
            error_message: 错误信息
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with db.session_marker() as session:
                record = session.query(DocumentTranslationRecord).filter_by(task_id=task_id).first()
                if record:
                    record.translation_status = "failed"
                    record.error_message = error_message
                    record.end_time = datetime.now()
                    
                    # 计算处理耗时(毫秒)
                    if record.start_time:
                        delta = record.end_time - record.start_time
                        record.process_time = int(delta.total_seconds() * 1000)
                    
                    logger.info(f"更新翻译错误信息成功，任务ID: {task_id}")
                    return True
                else:
                    logger.warning(f"未找到翻译任务记录，任务ID: {task_id}")
                    return False
        except Exception as e:
            logger.error(f"更新翻译错误信息失败: {str(e)}")
            return False
    
    @staticmethod
    def get_translation_record(task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取翻译记录
        
        Args:
            task_id: 翻译任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 翻译记录信息，如果未找到则返回None
        """
        try:
            with db.session_marker() as session:
                record = session.query(DocumentTranslationRecord).filter_by(task_id=task_id).first()
                if record:
                    return {
                        "id": record.id,
                        "task_id": record.task_id,
                        "user_id": record.user_id,
                        "task_type": record.task_type,
                        "original_filename": record.original_filename,
                        "original_file_ftp_url": record.original_file_ftp_url,
                        "translated_filename": record.translated_filename,
                        "translated_file_ftp_url": record.translated_file_ftp_url,
                        "source_language": record.source_language,
                        "target_language": record.target_language,
                        "translation_progress": record.translation_progress,
                        "translation_status": record.translation_status,
                        "error_message": record.error_message,
                        "start_time": record.start_time.isoformat() if record.start_time else None,
                        "end_time": record.end_time.isoformat() if record.end_time else None,
                        "process_time": record.process_time,
                        "created_at": record.created_at.isoformat() if record.created_at else None,
                        "updated_at": record.updated_at.isoformat() if record.updated_at else None
                    }
                else:
                    logger.warning(f"未找到翻译任务记录，任务ID: {task_id}")
                    return None
        except Exception as e:
            logger.error(f"获取翻译记录失败: {str(e)}")
            return None
    
    @staticmethod
    def get_translation_records(
        user_id: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 10,
        offset: int = 0,
        task_type: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取翻译记录列表
        
        Args:
            user_id: 用户ID，可选，用于筛选特定用户的记录
            status: 翻译状态，可选，用于筛选特定状态的记录
            limit: 每页记录数，默认为10
            offset: 偏移量，默认为0
            task_type: 任务类型，可选，用于筛选特定类型的记录 ("translation"-翻译, "conversion"-转换)
            
        Returns:
            Tuple[List[Dict[str, Any]], int]: 翻译记录列表和总记录数
        """
        try:
            with db.session_marker() as session:
                query = session.query(DocumentTranslationRecord)
                
                # 应用筛选条件
                if user_id:
                    query = query.filter(DocumentTranslationRecord.user_id == user_id)
                if status:
                    query = query.filter(DocumentTranslationRecord.translation_status == status)
                if task_type:
                    query = query.filter(DocumentTranslationRecord.task_type == task_type)
                
                # 获取总记录数
                total_count = query.count()
                
                # 应用分页并按创建时间降序排序
                records = query.order_by(DocumentTranslationRecord.created_at.desc()) \
                              .limit(limit).offset(offset).all()
                
                result = []
                for record in records:
                    result.append({
                        "id": record.id,
                        "task_id": record.task_id,
                        "user_id": record.user_id,
                        "task_type": record.task_type,
                        "original_filename": record.original_filename,
                        "original_file_ftp_url": record.original_file_ftp_url,
                        "translated_filename": record.translated_filename,
                        "translated_file_ftp_url": record.translated_file_ftp_url,
                        "source_language": record.source_language,
                        "target_language": record.target_language,
                        "translation_progress": record.translation_progress,
                        "translation_status": record.translation_status,
                        "start_time": record.start_time.isoformat() if record.start_time else None,
                        "end_time": record.end_time.isoformat() if record.end_time else None,
                        "process_time": record.process_time,
                        "created_at": record.created_at.isoformat() if record.created_at else None
                    })
                
                return result, total_count
        except Exception as e:
            logger.error(f"获取翻译记录列表失败: {str(e)}")
            return [], 0
