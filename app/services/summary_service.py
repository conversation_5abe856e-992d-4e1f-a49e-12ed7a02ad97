from loguru import logger
from typing import Optional, Callable
from app.utils.rag.document_loader import DocumentLoader
from app.utils.rag.text_processor import TextProcessor
from app.utils.rag.summarizer import Summarizer


class SummaryService:
    """文档摘要服务类，使用LangChain和Ollama实现Word、PDF和PPT文档摘要。"""

    def __init__(self, model_name: str = "qwen2.5:14b", chunk_size: int = 1000, chunk_overlap: int = 100, temperature: float = 0.5, chain_type: str = "map_reduce"):
        """初始化SummaryService。

        Args:
            model_name: Ollama模型名称
            chunk_size: 文本分割块大小
            chunk_overlap: 文本分割块重叠大小
            temperature: 生成温度
            chain_type: 摘要链类型
        """
        self.model_name = model_name
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.temperature = temperature
        self.chain_type = chain_type

    def summarize(self, file_path: str, progress_callback: Optional[Callable[[float], None]] = None, chunk_count_callback: Optional[Callable[[int], None]] = None) -> str:
        """摘要文档。

        Args:
            file_path: 要摘要的文档路径
            progress_callback: 可选的进度回调函数，接收0-1之间的浮点数表示进度
            chunk_count_callback: 可选的回调函数，接收文档段落总数

        Returns:
            文档摘要
        """
        # 1. 加载文档
        documents = DocumentLoader.load_document(file_path)
        logger.info(f"成功加载文档，共 {len(documents)} 页/部分")

        # 2. 处理文本
        text_processor = TextProcessor(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap
        )
        split_docs = text_processor.split_documents(documents)
        logger.info(f"文本分割完成，共 {len(split_docs)} 个文本块")
        
        # 如果提供了段落计数回调函数，通知段落总数
        if chunk_count_callback:
            chunk_count_callback(len(split_docs))

        # 3. 生成摘要
        summarizer = Summarizer(
            model_name=self.model_name,
            temperature=self.temperature
        )
        # 根据文档字符长度，选择合适的摘要链类型
        contextx = "\n".join([doc.page_content for doc in documents])
        chain_type = self.chain_type if len(contextx) > 1000 else "stuff"
        summary = summarizer.summarize(
            documents=split_docs,
            chain_type=chain_type,
            callback=progress_callback  # 传递进度回调函数
        )

        # 4. 输出结果
        logger.info("\n" + "-" * 50)
        logger.info("文档摘要：")
        logger.info(summary)
        logger.info("-" * 50 + "\n")

        return summary