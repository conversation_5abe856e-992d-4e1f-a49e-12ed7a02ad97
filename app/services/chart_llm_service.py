import json
from loguru import logger
from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy import func
from app.models.db import DatabaseConnection
from app.models.chart_llm_record import ChartLLMRecord


db = DatabaseConnection()


def record_chart_llm_request(
    user_id: Optional[str],
    query: str,
    prompt: str,
    model_params: Dict[str, Any],
    result_content: Optional[str] = None,
    process_time: Optional[int] = None,
    status: str = "success",
    error_message: Optional[str] = None
) -> int:
    """记录图表LLM生成请求到数据库
    
    Args:
        user_id: 用户ID
        query: 用户查询内容
        prompt: 完整的提示词
        model_params: 模型调用参数字典
        result_content: 生成的结果内容
        process_time: 处理耗时(毫秒)
        status: 处理状态
        error_message: 错误信息
        
    Returns:
        int: 记录ID
    """
    try:
        db.log_pool_status()  # 记录操作前的连接池状态
        
        with db.session_marker() as session:
            record = ChartLLMRecord(
                user_id=user_id,
                query=query,
                prompt=prompt,
                model_params=model_params,
                result_content=result_content,
                process_time=process_time,
                status=status,
                error_message=error_message
            )
            session.add(record)
            session.commit()
            logger.info(f"成功记录图表LLM生成请求: 用户 {user_id}, 状态 {status}")
            return record.id
    except Exception as e:
        logger.error(f"记录图表LLM生成请求失败: {str(e)}")
        raise
    finally:
        db.log_pool_status()  # 记录操作后的连接池状态