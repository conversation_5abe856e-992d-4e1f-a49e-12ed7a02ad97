import uuid
from loguru import logger
from sqlalchemy import func, desc, and_
from typing import List, Dict, Any, Optional
from datetime import datetime

from app.core.config import settings
from app.models.db import DatabaseConnection
from app.models.message_record import MessageRecord


db = DatabaseConnection()

def record_message(
    user_id: str,
    message_content: str,
    response_content: str,
    message_type: str,
    process_time: int = None,
    status: int = 1,
    error_message: str = None,
    session_id: str = None,
    session_name: str = None,
    attachment_url: str = None
) -> str:
    """记录消息到数据库

    Args:
        user_id: 用户ID
        message_content: 用户发送的消息内容
        response_content: 系统的回复内容
        message_type: 消息类型
        process_time: 处理耗时(毫秒)
        status: 处理状态（1-成功，0-失败）
        error_message: 错误信息
        session_id: 会话ID，如果为None则自动生成
        session_name: 会话名称
    
    Returns:
        str: 会话ID
    """
    try:
        db.log_pool_status()  # 记录操作前的连接池状态

        # 如果没有提供session_id，则自动生成一个
        if not session_id:
            session_id = str(uuid.uuid4())

        with db.session_marker() as session:
            record = MessageRecord(
                user_id=user_id,
                session_id=session_id,
                session_name=session_name,
                message_content=message_content,
                response_content=response_content,
                message_type=message_type,
                process_time=process_time,
                status=status,
                error_message=error_message,
                environment=settings.ENVIRONMENT,
                attachment_url=attachment_url
            )
            session.add(record)
            session.commit()
            logger.info("成功记录消息到数据库")
            return record.session_id
    except Exception as e:
        logger.error("记录消息到数据库失败: {}", str(e))
        raise
    finally:
        db.log_pool_status()  # 记录操作后的连接池状态


def get_user_conversations(user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
    """获取用户的所有会话列表（仅未删除的记录）
    
    Args:
        user_id: 用户ID
        limit: 限制返回的会话数量
        
    Returns:
        List[Dict]: 会话列表，包含id, title, sessionId, createdAt, updatedAt, messages
    """
    try:
        with db.session_marker() as session:
            # 查询每个session_id的最新消息，用于获取会话基本信息（仅未删除的记录）
            subquery = session.query(
                MessageRecord.session_id,
                MessageRecord.session_name,
                func.min(MessageRecord.created_at).label('created_at'),
                func.max(MessageRecord.updated_at).label('updated_at'),
                func.count(MessageRecord.id).label('message_count')
            ).filter(
                and_(
                    MessageRecord.user_id == user_id,
                    MessageRecord.is_deleted == False  # 仅查询未删除的记录
                )
            ).group_by(
                MessageRecord.session_id
            ).order_by(
                desc('created_at')  # 改为按创建时间倒序排序
            ).limit(limit).all()
            
            conversations = []
            for row in subquery:
                # 获取该会话的所有消息（仅未删除的记录）
                messages = session.query(MessageRecord).filter(
                    and_(
                        MessageRecord.user_id == user_id,
                        MessageRecord.session_id == row.session_id,
                        MessageRecord.is_deleted == False  # 仅查询未删除的记录
                    )
                ).order_by(MessageRecord.created_at).all()
                
                # 转换消息格式
                formatted_messages = []
                for msg in messages:
                    # 用户消息
                    if msg.message_content:
                        formatted_messages.append({
                            'id': f"user_{msg.id}",
                            'role': 'user',
                            'content': msg.message_content,
                            'timestamp': int(msg.created_at.timestamp() * 1000),
                            'attachments': [{'url': msg.attachment_url}] if msg.attachment_url else []
                        })
                    
                    # 助手消息
                    if msg.response_content:
                        formatted_messages.append({
                            'id': f"assistant_{msg.id}",
                            'role': 'assistant', 
                            'content': msg.response_content,
                            'timestamp': int(msg.created_at.timestamp() * 1000),
                            'processTime': msg.process_time
                        })
                
                # 生成会话标题
                title = row.session_name or f"对话 {row.created_at.strftime('%m-%d %H:%M')}"
                
                conversations.append({
                    'id': row.session_id,
                    'title': title,
                    'sessionId': row.session_id,
                    'createdAt': int(row.created_at.timestamp() * 1000),
                    'updatedAt': int(row.updated_at.timestamp() * 1000),
                    'messages': formatted_messages
                })
            
            logger.info(f"获取用户会话列表成功，用户ID: {user_id}，会话数量: {len(conversations)}")
            return conversations
            
    except Exception as e:
        logger.error(f"获取用户会话列表失败: {str(e)}")
        raise


def create_conversation(user_id: str, title: str, session_id: str) -> Dict[str, Any]:
    """创建新会话
    
    Args:
        user_id: 用户ID
        title: 会话标题
        session_id: 会话ID
        
    Returns:
        Dict: 创建的会话信息
    """
    try:
        # 直接返回会话信息，不创建空白记录
        # 会话将在第一条消息时自动创建
        created_at = int(datetime.now().timestamp() * 1000)
        
        conversation = {
            'id': session_id,
            'title': title,
            'sessionId': session_id,
            'createdAt': created_at,
            'updatedAt': created_at,
            'messages': []
        }
        
        logger.info(f"创建会话成功（仅返回会话结构），用户ID: {user_id}，会话ID: {session_id}")
        return conversation
            
    except Exception as e:
        logger.error(f"创建会话失败: {str(e)}")
        raise


def update_conversation(user_id: str, session_id: str, title: str = None) -> Dict[str, Any]:
    """更新会话信息
    
    Args:
        user_id: 用户ID
        session_id: 会话ID
        title: 新的会话标题
        
    Returns:
        Dict: 更新后的会话信息
    """
    try:
        with db.session_marker() as session:
            # 更新该会话的所有消息记录的session_name（仅未删除的记录）
            if title:
                updated_count = session.query(MessageRecord).filter(
                    and_(
                        MessageRecord.user_id == user_id,
                        MessageRecord.session_id == session_id,
                        MessageRecord.is_deleted == False  # 仅更新未删除的记录
                    )
                ).update({
                    'session_name': title,
                    'updated_at': datetime.now()
                })
                session.commit()
            
            # 获取更新后的会话信息（仅未删除的记录）
            latest_record = session.query(MessageRecord).filter(
                and_(
                    MessageRecord.user_id == user_id,
                    MessageRecord.session_id == session_id,
                    MessageRecord.is_deleted == False  # 仅查询未删除的记录
                )
            ).order_by(desc(MessageRecord.updated_at)).first()
            
            if not latest_record:
                # 如果会话还没有任何消息记录，返回一个默认的会话信息
                # 这种情况在新建会话但还没有发送消息时会出现
                logger.info(f"会话暂无消息记录，返回默认信息: {session_id}")
                return {
                    'id': session_id,
                    'title': title or "新会话",
                    'sessionId': session_id,
                    'updatedAt': int(datetime.now().timestamp() * 1000)
                }
            
            conversation = {
                'id': session_id,
                'title': latest_record.session_name or title,
                'sessionId': session_id,
                'updatedAt': int(latest_record.updated_at.timestamp() * 1000)
            }
            
            logger.info(f"更新会话成功，用户ID: {user_id}，会话ID: {session_id}")
            return conversation
            
    except Exception as e:
        logger.error(f"更新会话失败: {str(e)}")
        raise


def delete_conversation(user_id: str, session_id: str) -> bool:
    """软删除会话（通过标记删除而不是真正删除）
    
    Args:
        user_id: 用户ID
        session_id: 会话ID
        
    Returns:
        bool: 删除是否成功
    """
    try:
        with db.session_marker() as session:
            # 软删除该会话的所有消息记录，设置is_deleted=True
            updated_count = session.query(MessageRecord).filter(
                and_(
                    MessageRecord.user_id == user_id,
                    MessageRecord.session_id == session_id,
                    MessageRecord.is_deleted == False  # 只更新未删除的记录
                )
            ).update({
                'is_deleted': True,
                'updated_at': datetime.now()
            })
            
            session.commit()
            
            logger.info(f"软删除会话成功，用户ID: {user_id}，会话ID: {session_id}，标记删除消息数: {updated_count}")
            return updated_count > 0
            
    except Exception as e:
        logger.error(f"软删除会话失败: {str(e)}")
        raise


def get_conversation_messages(user_id: str, session_id: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """获取会话的消息列表（仅未删除的记录）
    
    Args:
        user_id: 用户ID
        session_id: 会话ID
        limit: 限制返回的消息数量
        offset: 偏移量
        
    Returns:
        List[Dict]: 消息列表
    """
    try:
        with db.session_marker() as session:
            messages = session.query(MessageRecord).filter(
                and_(
                    MessageRecord.user_id == user_id,
                    MessageRecord.session_id == session_id,
                    MessageRecord.is_deleted == False  # 仅查询未删除的记录
                )
            ).order_by(MessageRecord.created_at).offset(offset).limit(limit).all()
            
            formatted_messages = []
            for msg in messages:
                # 用户消息
                if msg.message_content:
                    formatted_messages.append({
                        'id': f"user_{msg.id}",
                        'role': 'user',
                        'content': msg.message_content,
                        'timestamp': int(msg.created_at.timestamp() * 1000),
                        'attachments': [{'url': msg.attachment_url}] if msg.attachment_url else []
                    })
                
                # 助手消息
                if msg.response_content:
                    formatted_messages.append({
                        'id': f"assistant_{msg.id}",
                        'role': 'assistant',
                        'content': msg.response_content,
                        'timestamp': int(msg.created_at.timestamp() * 1000),
                        'processTime': msg.process_time
                    })
            
            logger.info(f"获取会话消息成功，用户ID: {user_id}，会话ID: {session_id}，消息数量: {len(formatted_messages)}")
            return formatted_messages
            
    except Exception as e:
        logger.error(f"获取会话消息失败: {str(e)}")
        raise


def add_message_to_conversation(
    user_id: str, 
    session_id: str, 
    role: str, 
    content: str,
    thinking: str = None,
    process_time: int = None,
    attachments: List[Dict] = None,
    timestamp: int = None
) -> Dict[str, Any]:
    """添加消息到会话
    
    Args:
        user_id: 用户ID
        session_id: 会话ID
        role: 消息角色 (user/assistant)
        content: 消息内容
        thinking: 思考过程
        process_time: 处理时间
        attachments: 附件列表
        timestamp: 时间戳
        
    Returns:
        Dict: 创建的消息信息
    """
    try:
        with db.session_marker() as session:
            # 获取会话名称
            existing_record = session.query(MessageRecord).filter(
                and_(
                    MessageRecord.user_id == user_id,
                    MessageRecord.session_id == session_id,
                    MessageRecord.is_deleted == False  # 只查询未删除的记录
                )
            ).first()
            
            session_name = existing_record.session_name if existing_record else None
            attachment_url = None
            
            if attachments and len(attachments) > 0:
                # 取第一个附件的URL
                attachment_url = attachments[0].get('url')
            
            # 根据角色设置消息内容
            # 只有当内容不为空时才创建记录
            if content and content.strip():
                message_content = content if role == 'user' else ""
                response_content = content if role == 'assistant' else ""
                
                record = MessageRecord(
                    user_id=user_id,
                    session_id=session_id,
                    session_name=session_name,
                    message_content=message_content,
                    response_content=response_content,
                    message_type="text",
                    process_time=process_time,
                    status=1,
                    environment=settings.ENVIRONMENT,
                    attachment_url=attachment_url
                )
                
                session.add(record)
                session.commit()
                
                message = {
                    'id': f"{role}_{record.id}",
                    'role': role,
                    'content': content,
                    'timestamp': int(record.created_at.timestamp() * 1000),
                    'thinking': thinking,
                    'processTime': process_time,
                    'attachments': attachments or []
                }
                
                logger.info(f"添加消息到会话成功，用户ID: {user_id}，会话ID: {session_id}")
                return message
            else:
                # 如果内容为空，返回一个虚拟消息，但不保存到数据库
                logger.info(f"消息内容为空，跳过保存，用户ID: {user_id}，会话ID: {session_id}")
                return {
                    'id': f"{role}_temp_{int(datetime.now().timestamp() * 1000)}",
                    'role': role,
                    'content': content,
                    'timestamp': int(datetime.now().timestamp() * 1000),
                    'thinking': thinking,
                    'processTime': process_time,
                    'attachments': attachments or []
                }
            
    except Exception as e:
        logger.error(f"添加消息到会话失败: {str(e)}")
        raise