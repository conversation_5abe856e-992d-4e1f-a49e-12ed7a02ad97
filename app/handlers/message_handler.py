"""消息处理器"""
import time
from loguru import logger
from typing import Any, Tu<PERSON>
from app.core.chat_cache import chat_cache
from app.services.message_service import record_message
from app.utils.message_utils import is_markdown_format
from app.services.wechat_service import WeChatService
from app.services.mengchat_service import MengChatService


wechat_service = WeChatService()
mengchat_service = MengChatService()

class MessageHandler:
    """消息处理器基类"""
    
    async def handle(self, message: Any, start_time: float) -> Tuple[str, int]:
        """处理消息
        
        Args:
            message: 消息对象
            start_time: 开始处理时间
            
        Returns:
            Tuple[str, int]: 返回处理结果和处理时间
        """
        raise NotImplementedError

class TextMessageHandler(MessageHandler):
    """文本消息处理器"""
    
    async def handle(self, message: Any, start_time: float) -> Tuple[str, int]:
        """处理文本消息
        
        Args:
            message: 消息对象
            start_time: 开始处理时间
            
        Returns:
            Tuple[str, int]: 返回处理结果和处理时间
        """
        logger.info("处理文本消息, 内容: {}", message.Content)
        
        # 从缓存获取用户最后一条消息
        last_image_content = await chat_cache.get_last_image_content(message.FromUserName)

        # 拼接当前消息和最后一条图片消息
        if last_image_content:
            combined_content = f"{last_image_content}\n\n请根据图片OCR识别结果回答下面的问题:\n{message.Content}"
            msg = {'role': 'user', 'content': combined_content}
        else:
            msg = {'role': 'user', 'content': message.Content}

        response = await mengchat_service.process_message(message.FromUserName, msg)
        reply_content = response.content
        process_time = int((time.time() - start_time) * 1000)
        
        logger.info("MengChat处理完成，响应内容长度: {}, 响应内容：{}, 处理耗时: {}ms", 
                   len(reply_content), reply_content, process_time)
        
        # 记录消息
        try:
            record_message(
                user_id=message.FromUserName,
                message_content=message.Content,
                response_content=reply_content,
                message_type=message.MsgType,
                process_time=process_time
            )
        except Exception as e:
            logger.error("记录消息到数据库失败，继续处理消息: {}", str(e))
        
        try:
            # 检查是否markdown格式消息
            is_markdown = is_markdown_format(reply_content)
            # 发送对应格式的消息
            if '```mermaid' in reply_content:
                logger.info("回复内容为mermaid图像信息，发送mermaid消息")
                wechat_service.send_message(message.FromUserName, reply_content, msg_type="mermaid")
            elif is_markdown and ('最近的待办任务' in reply_content or '最近的已办任务' in reply_content or '流程的最新状态' in reply_content):
                logger.info("回复内容为markdown格式流程信息，发送mpnews消息")
                wechat_service.send_message(message.FromUserName, reply_content, msg_type="process")
            elif is_markdown:
                logger.info("回复内容为markdown格式，发送markdown消息")
                wechat_service.send_message(message.FromUserName, reply_content, msg_type="markdown")
            else:
                logger.info("回复内容为文本消息，发送文本消息")
                wechat_service.send_message(message.FromUserName, reply_content, msg_type="text")
        except Exception as e:
            logger.error("发送企微消息失败: {}", str(e))
            # 更新消息记录状态为失败
            try:
                record_message(
                    user_id=message.FromUserName,
                    message_content=message.Content,
                    response_content=reply_content,
                    message_type=message.MsgType,
                    process_time=process_time,
                    status=0,
                    error_message=str(e)
                )
            except Exception as e:
                logger.error("更新消息记录状态失败: {}", str(e))
            # 如果发送企微消息失败，返回默认消息
            wechat_service.send_message(message.FromUserName, "不好意思，我无法回答这个问题！")
            
        # 清理缓存中的历史记录
        chat_cache.clear_user(message.FromUserName)
        logger.info("清理用户 {} 的缓存历史记录", message.FromUserName)
        
        return reply_content, process_time

class ImageMessageHandler(MessageHandler):
    """图片消息处理器"""
    
    async def handle(self, message: Any, start_time: float) -> Tuple[str, int]:
        """处理图片消息
        
        Args:
            message: 消息对象
            start_time: 开始处理时间
            
        Returns:
            Tuple[str, int]: 返回处理结果和处理时间
        """
        # 先将图片消息处理状态存入缓存
        chat_cache.add_message(message.FromUserName, "image", "process")
        # 调用MengChat处理消息
        msg = {'role': 'user', 'content': [{"type": "image_url", "image_url": {"url": message.PicUrl}}]}
        response = await mengchat_service.process_message(message.FromUserName, msg)
        reply_content = response.content
        process_time = int((time.time() - start_time) * 1000)
        
        logger.info("MengChat处理完成，响应内容长度: {}, 响应内容：{}, 处理耗时: {}ms", 
                   len(reply_content), reply_content, process_time)
        
        # 记录图片消息
        try:
            record_message(
                user_id=message.FromUserName,
                message_content=message.PicUrl,
                response_content=reply_content,
                message_type=message.MsgType,
                process_time=process_time
            )
        except Exception as e:
            logger.error("记录消息到数据库失败，继续处理消息: {}", str(e))
        
        try:
            # 将回复内容存入缓存
            chat_cache.add_message(message.FromUserName, "image", reply_content)
            logger.info("成功将回复内容存入缓存")
            
            wechat_service.send_message(message.FromUserName, reply_content)
            logger.info("成功发送文本消息")
        except Exception as e:
            logger.error("发送文本消息失败: {}", str(e))
            # 更新消息记录状态为失败
            try:
                record_message(
                    user_id=message.FromUserName,
                    message_content=message.PicUrl,
                    response_content=reply_content,
                    message_type=message.MsgType,
                    process_time=process_time,
                    status=0,
                    error_message=str(e)
                )
            except Exception as e:
                logger.error("更新消息记录状态失败: {}", str(e))
            
        return reply_content, process_time

class EventMessageHandler(MessageHandler):
    """事件消息处理器"""
    
    async def handle(self, message: Any, start_time: float) -> Tuple[str, int]:
        """处理事件消息
        
        Args:
            message: 消息对象
            start_time: 开始处理时间
            
        Returns:
            Tuple[str, int]: 返回处理结果和处理时间
        """
        logger.info("处理事件消息, 事件类型: {}", message.Event)
        
        if message.Event == "subscribe" or message.Event == "enter_agent":
            # reply_content = "欢迎关注！我是MengChat，一个基于人工智能的智能助手。我可以帮你回答问题、提供建议，让我们开始愉快的对话吧！"
            process_time = int((time.time() - start_time) * 1000)
            
            try:
                # 获取帮助信息
                msg = {'role': 'user', 'content': "/help"}
                response = await mengchat_service.process_message(message.FromUserName, msg)
                reply_content = response.content

                # 发送欢迎消息
                wechat_service.send_message(message.FromUserName, reply_content, msg_type="markdown")
                
                # 记录消息
                record_message(
                    user_id=message.FromUserName,
                    message_content="订阅应用",
                    response_content=reply_content,
                    message_type=message.MsgType,
                    process_time=process_time
                )
            except Exception as e:
                logger.error("处理订阅事件失败: {}", str(e))
                error_msg = "很抱歉，处理您的请求时出现了错误。请稍后再试。"
                # 更新消息记录状态为失败
                try:
                    record_message(
                        user_id=message.FromUserName,
                        message_content="订阅应用",
                        response_content=error_msg,
                        message_type=message.MsgType,
                        process_time=process_time,
                        status=0,
                        error_message=str(e)
                    )
                except Exception as e:
                    logger.error("更新消息记录状态失败: {}", str(e))
                return error_msg, process_time
            
            return reply_content, process_time
        else:
            logger.info("不支持的事件类型: {}", message.Event)
            return "不支持的事件类型", 0

class UnsupportedMessageHandler(MessageHandler):
    """不支持的消息类型处理器"""
    
    async def handle(self, message: Any, start_time: float) -> Tuple[str, int]:
        """处理不支持的消息类型
        
        Args:
            message: 消息对象
            start_time: 开始处理时间
            
        Returns:
            Tuple[str, int]: 返回处理结果和处理时间
        """
        process_time = int((time.time() - start_time) * 1000)
        logger.info("消息类型不支持")
        
        reply_content = "消息类型不支持"
        
        # 记录不支持的消息类型
        try:
            record_message(
                user_id=message.FromUserName,
                message_content="不支持的消息类型",
                response_content=reply_content,
                message_type=message.MsgType,
                process_time=process_time,
                status=0,
                error_message="Unsupported message type"
            )
        except Exception as e:
            logger.error("记录消息到数据库失败，继续处理消息: {}", str(e))
        
        return reply_content, process_time

def get_message_handler(message_type: str) -> MessageHandler:
    """获取消息处理器
    
    Args:
        message_type: 消息类型
        
    Returns:
        MessageHandler: 对应类型的消息处理器
    """
    handlers = {
        "text": TextMessageHandler(),
        "image": ImageMessageHandler(),
        "event": EventMessageHandler()
    }
    return handlers.get(message_type, UnsupportedMessageHandler())
