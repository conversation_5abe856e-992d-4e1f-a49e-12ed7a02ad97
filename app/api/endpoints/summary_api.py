import os
import uuid
import asyncio
from loguru import logger
from typing import Optional, Callable
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, Header
from fastapi.responses import JSONResponse
import threading

from app.services.summary_service import SummaryService
from app.services.document_summary_service import DocumentSummaryService
from app.utils.ftp_utils import upload_local_file_to_ftp

# 存储摘要生成进度的字典
summary_progress = {}
# 存储文档段落总数的字典
document_chunks = {}

router = APIRouter()

# 验证API密钥
def verify_api_key(authorization: str = Header(...)):
    api_key = os.environ.get("API_KEY", "c2stbWVuZ2NoYXQtYm90IQ==")
    if authorization != f"Bearer {api_key}":
        raise HTTPException(status_code=401, detail="无效的API密钥")
    return True


async def process_document_summary(task_id: str, file_path: str, model_name: str = "qwen2.5:14b") -> dict:
    """处理文档摘要生成，并更新进度
    
    Args:
        task_id: 任务ID
        file_path: 文件路径
        model_name: 模型名称
        
    Returns:
        dict: 摘要结果
    """
    try:
        # 更新进度为开始处理
        summary_progress[task_id] = {
            "status": "processing",
            "progress": 10,
            "message": "开始处理文档"
        }
        
        # 更新数据库中的进度状态
        DocumentSummaryService.update_summary_progress(
            task_id=task_id,
            progress=10,
            status="processing"
        )
        
        # 创建进度回调函数，将摘要器的进度更新到任务进度字典
        def update_progress(progress: float):
            # 将0-1的进度转换为10-90的百分比进度
            # 保留开始的10%和结束的10%给其他处理步骤
            percent_progress = int(10 + progress * 80)
            
            # 获取文档实际的段落总数
            total_chunks = document_chunks.get(task_id, 10)  # 默认值为10
            
            # 计算已处理的段落数
            processed_chunks = int(progress * total_chunks)
            # 确保显示至少1段（除非进度为0）
            if progress > 0 and processed_chunks == 0:
                processed_chunks = 1
            
            # 更新全局进度字典
            summary_progress[task_id] = {
                "status": "processing",
                "progress": percent_progress,
                "message": f"正在生成摘要，已完成 {processed_chunks}/{total_chunks} 段文本摘要"
            }
            logger.info(f"任务 {task_id} 进度更新: {percent_progress}%, 已处理 {processed_chunks}/{total_chunks} 段文本")
            
            # 每20%更新一次数据库中的进度信息
            if percent_progress % 20 == 0 or percent_progress == 100:
                DocumentSummaryService.update_summary_progress(
                    task_id=task_id,
                    progress=percent_progress,
                    status="processing",
                )
        
        # 创建摘要服务实例，并传入进度回调函数
        summary_service = SummaryService(model_name=model_name)
        
        # 更新进度为文档加载中
        summary_progress[task_id] = {
            "status": "processing",
            "progress": 10,
            "message": "文档加载中"
        }
        
        # 使用摘要服务生成摘要，传入进度回调函数
        # 由于summarize可能会阻塞事件循环，我们使用线程池来执行它
        def run_summarize():
            try:
                # 调用摘要服务之前或之中，需要获取文档段落总数并保存
                # 由于文档段落总数需要在文档加载和处理后才能确定
                # 这里注册一个特殊回调函数来捕获段落总数
                def chunk_count_callback(count: int):
                    document_chunks[task_id] = count
                    logger.info(f"任务 {task_id} 文档段落总数: {count}")
                    # 更新数据库中的段落总数
                    DocumentSummaryService.update_summary_progress(
                        task_id=task_id,
                        progress=summary_progress[task_id].get("progress", 10),
                        status="processing",
                    )
                
                # 把传递段落总数的回调函数传给summary_service
                result = summary_service.summarize(
                    file_path, 
                    progress_callback=update_progress,
                    chunk_count_callback=chunk_count_callback
                )
                
                # 更新进度为完成
                summary_data = {
                    "summary": result,
                    "filename": os.path.basename(file_path)
                }
                
                # 获取总段数
                total_chunks = document_chunks.get(task_id, 10)
                
                summary_progress[task_id] = {
                    "status": "completed",
                    "progress": 100,
                    "message": f"摘要生成完成，共处理 {total_chunks} 段文本",
                    "result": summary_data
                }
                logger.info(f"任务 {task_id} 摘要生成完成，共处理 {total_chunks} 段文本")
                
                # 更新数据库中的摘要结果
                DocumentSummaryService.update_summary_result(
                    task_id=task_id,
                    summary_content=result
                )
            except Exception as e:
                error_message = f"摘要生成失败: {str(e)}"
                summary_progress[task_id] = {
                    "status": "failed",
                    "progress": 0,
                    "message": error_message
                }
                logger.error(f"任务 {task_id} {error_message}")
                
                # 更新数据库中的错误信息
                DocumentSummaryService.update_summary_error(
                    task_id=task_id,
                    error_message=error_message
                )
        
        # 在线程中运行摘要生成，不阻塞事件循环
        thread = threading.Thread(target=run_summarize)
        thread.daemon = True
        thread.start()
        
        # 等待线程完成
        while thread.is_alive():
            # 每隔0.5秒检查一次，同时允许其他协程运行
            await asyncio.sleep(0.5)
        
        return summary_progress[task_id]
    except Exception as e:
        # 更新进度为失败
        error_message = f"摘要生成失败: {str(e)}"
        summary_progress[task_id] = {
            "status": "failed",
            "progress": 0,
            "message": error_message
        }
        
        # 更新数据库中的错误信息
        DocumentSummaryService.update_summary_error(
            task_id=task_id,
            error_message=error_message
        )
        
        logger.error(error_message)
        raise e


@router.post("/api/document/summary")
async def generate_document_summary(
    file: UploadFile = File(...),
    model_name: str = Form("qwen2.5:14b"),
    user_id: str = Form(""),
    api_key_valid: bool = Depends(verify_api_key)
):
    """
    文档摘要生成接口

    接收上传的PDF、Word或PPT文档，生成文档摘要

    Args:
        file: 上传的文档文件（支持PDF、Word和PPT格式）
        model_name: 使用的模型名称，默认为"qwen2.5:14b"
        user_id: 用户ID

    Returns:
        JSONResponse: 包含任务ID的JSON响应
    """
    try:
        task_id = str(uuid.uuid4())
        logger.info(f"接收到文档摘要请求: {file.filename}, 任务ID: {task_id}, 模型: {model_name}")

        # 检查文件类型
        if not file.filename.endswith(('.pdf', '.docx', '.pptx')):
            raise HTTPException(status_code=400, detail="仅支持PDF、Word文档格式(.pdf, .docx)和PPT文档格式(.pptx)")
            
        # 确保临时目录存在
        os.makedirs("./temp/summaries", exist_ok=True)

        temp_file_path = f"./temp/summaries/{file.filename}"

        try:
            # 保存上传的文件
            with open(temp_file_path, "wb") as temp_file:
                content = await file.read()
                temp_file.write(content)
            
            # 创建数据库记录
            DocumentSummaryService.create_summary_record(
                task_id=task_id,
                original_filename=file.filename,
                user_id=user_id
            )
            
            # 将原始文件上传到FTP服务器（不影响主流程）
            async def upload_original_file():
                success, message, data = await upload_local_file_to_ftp(
                    temp_file_path,
                    file.filename,
                    file.content_type
                )
                if success and data and "imgUrl" in data:
                    # 更新原始文件的FTP URL
                    DocumentSummaryService.update_original_file_ftp_url(task_id, data["imgUrl"])
            
            # 异步上传原始文件
            asyncio.create_task(upload_original_file())
            
            # 初始化进度
            summary_progress[task_id] = {
                "status": "started",
                "progress": 0,
                "message": "开始处理文档"
            }
            
            # 创建后台摘要生成任务
            async def background_summary_task():
                try:
                    await process_document_summary(task_id, temp_file_path, model_name)
                except Exception as e:
                    # 更新进度为失败
                    summary_progress[task_id] = {
                        "status": "failed",
                        "progress": 0,
                        "message": f"摘要生成失败: {str(e)}"
                    }
                    # 更新数据库中的错误信息
                    DocumentSummaryService.update_summary_error(
                        task_id=task_id,
                        error_message=str(e)
                    )
                    logger.error(f"后台摘要任务失败: {str(e)}")
            
            # 启动后台摘要任务，不等待其完成
            asyncio.create_task(background_summary_task())
            asyncio.create_task(cleanup_temp_files(temp_file_path, task_id))

            # 立即返回任务ID和状态
            return JSONResponse(
                content={
                    "code": 0,
                    "message": "success",
                    "data": {
                        "task_id": task_id,
                        "status": "processing",
                        "filename": file.filename
                    }
                }
            )

        except Exception as e:
            # 更新数据库中的错误信息
            DocumentSummaryService.update_summary_error(
                task_id=task_id,
                error_message=str(e)
            )
            # 清理临时文件
            if os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
            raise HTTPException(status_code=500, detail=f"摘要生成失败: {str(e)}")
    except Exception as e:
        logger.error(f"文档摘要生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文档摘要生成失败: {str(e)}")


@router.get("/api/document/summary/progress/{task_id}")
async def get_document_summary_progress(
    task_id: str,
    api_key_valid: bool = Depends(verify_api_key)
):
    """
    获取文档摘要生成进度
    
    Args:
        task_id: 摘要任务ID
        
    Returns:
        JSONResponse: 包含摘要进度信息的JSON响应
    """
    try:
        logger.info(f"接收到查询文档摘要进度请求，任务ID: {task_id}")
        
        # 从内存中获取进度
        progress_data = summary_progress.get(task_id)
        
        # 如果内存中没有进度信息，尝试从数据库查询
        if not progress_data:
            record = DocumentSummaryService.get_summary_record(task_id)
            if record:
                # 构建进度信息
                total_chunks = record.get("total_chunks", 0)
                processed_chunks = record.get("processed_chunks", 0)
                message = f"正在生成摘要，已完成 {processed_chunks}/{total_chunks} 段文本摘要" if record.get("summary_status") == "processing" else record.get("summary_status")
                
                # 如果摘要已完成，添加摘要内容
                if record.get("summary_status") == "completed":
                    progress_data = {
                        "status": record.get("summary_status"),
                        "progress": record.get("summary_progress", 100),
                        "message": f"摘要生成完成，共处理 {total_chunks} 段文本",
                        "result": {
                            "summary": record.get("summary_content"),
                            "filename": record.get("original_filename")
                        }
                    }
                else:
                    progress_data = {
                        "status": record.get("summary_status"),
                        "progress": record.get("summary_progress", 0),
                        "message": message
                    }
                    
                    # 如果状态是失败，添加错误信息
                    if record.get("summary_status") == "failed":
                        progress_data["message"] = record.get("error_message", "摘要生成失败")
            else:
                # 没有找到记录，返回未知状态
                progress_data = {
                    "status": "unknown",
                    "progress": 0,
                    "message": "未找到摘要任务"
                }
        
        # 构建响应数据
        response_data = {
            "code": 0,
            "message": "success",
            "data": progress_data
        }
        
        logger.info(f"查询文档摘要进度成功: {response_data}")
        return JSONResponse(content=response_data)
    except Exception as e:
        logger.error(f"查询文档摘要进度失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"查询文档摘要进度失败: {str(e)}"}
        )


@router.get("/api/document/summary/history")
async def get_document_summary_history(
    user_id: str,
    status: Optional[str] = None,
    page: int = 1,
    page_size: int = 10,
    api_key_valid: bool = Depends(verify_api_key)
):
    """
    获取文档摘要历史记录
    
    Args:
        user_id: 用户ID
        status: 摘要状态，可选 (pending-等待中, processing-处理中, completed-已完成, failed-失败)
        page: 页码，默认为1
        page_size: 每页记录数，默认为10
        
    Returns:
        JSONResponse: 包含摘要历史记录的JSON响应
    """
    try:
        logger.info(f"接收到查询文档摘要历史记录请求，用户ID: {user_id}, 状态: {status}, 页码: {page}, 每页记录数: {page_size}")
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询记录
        records, total_count = DocumentSummaryService.get_summary_records(
            user_id=user_id,
            status=status,
            limit=page_size,
            offset=offset
        )
        
        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0
        
        # 构建响应数据
        response_data = {
            "code": 0,
            "message": "success",
            "data": {
                "records": records,
                "pagination": {
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "current_page": page,
                    "page_size": page_size
                }
            }
        }
        
        return JSONResponse(content=response_data)
    except Exception as e:
        logger.error(f"查询文档摘要历史记录失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"查询文档摘要历史记录失败: {str(e)}"}
        ) 
    

async def cleanup_temp_files(temp_file_path: str, task_id: str):
    """
    清理临时文件的后台任务
    """
    try:
        # 等待更长时间，确保用户有足够时间下载文件
        logger.info(f"计划在 60 秒后清理临时文件: {temp_file_path}")
        await asyncio.sleep(60)  # 1分钟

        # 清理输入文件
        if os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
                logger.info(f"已清理临时文件: {temp_file_path}")
            except Exception as e:
                logger.error(f"清理临时文件失败: {temp_file_path}, 错误: {str(e)}")
        
        # 清理进度信息
        if task_id in summary_progress:
            del summary_progress[task_id]
            logger.info(f"已清理任务进度信息: {task_id}")
    except Exception as e:
        logger.error(f"清理临时文件失败: {str(e)}")