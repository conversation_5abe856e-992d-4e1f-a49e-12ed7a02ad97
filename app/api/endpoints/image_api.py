import base64
import requests
from pydantic import BaseModel

from loguru import logger
from fastapi import APIRouter, File, UploadFile
from app.utils.ftp_utils import upload_file_to_ftp


router = APIRouter()

class ImageUploadResponse(BaseModel):
    """图像上传响应模型"""
    success: bool
    message: str
    image_id: str = ""

@router.post("/api/image/upload", response_model=ImageUploadResponse)
async def upload_image(file: UploadFile = File(...)):
    """上传图像接口
    
    接收前端上传的图像文件，将其转发到指定的接口，并将响应转换为base64返回
    
    Args:
        file: 上传的图像文件
        
    Returns:
        ImageUploadResponse: 包含处理结果和base64编码的图像
    """
    try:
        logger.info(f"接收到图像上传请求: {file.filename}")
        
        # 使用ftp_utils上传文件
        success, message, data = await upload_file_to_ftp(file)
        
        if not success:
            return ImageUploadResponse(
                success=False,
                message=message
            )
        
        # 获取图像URL
        image_url = data["imgUrl"]
        # 将URL转为base64作为ID
        image_id = base64.b64encode(image_url.encode('utf-8')).decode('utf-8')

        logger.info(f"图像上传成功，URL地址：{image_url}  image_id：{image_id}")
        return ImageUploadResponse(
            success=True,
            message="图像上传成功",
            image_id=image_id
        )
        
    except Exception as e:
        error_msg = f"图像上传处理失败: {str(e)}"
        logger.error(error_msg)
        return ImageUploadResponse(
            success=False,
            message=error_msg
        )