import os
import json
import aiohttp
import asyncio
from loguru import logger
from typing import AsyncGenerator, Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, Header
from fastapi.responses import JSONResponse, FileResponse, StreamingResponse

from app.core.config import settings
from app.utils.ftp_utils import upload_local_file_to_ftp
from app.services.document_translation_service import DocumentTranslationService
from app.utils.word_trans_utils import translate_docx, register_progress_callback, remove_progress_callback, translate_filename
from app.utils.ppt_trans_utils import translate_pptx
from app.utils.excel_trans_utils import translate_excel
from app.utils.pdf_trans_utils import translate_pdf, register_pdf_progress_callback, remove_pdf_progress_callback


# 存储翻译进度的字典
translation_progress = {}

router = APIRouter()

# 验证API密钥
def verify_api_key(authorization: str = Header(...)):
    api_key = os.environ.get("API_KEY", "c2stbWVuZ2NoYXQtYm90IQ==")
    if authorization != f"Bearer {api_key}":
        raise HTTPException(status_code=401, detail="无效的API密钥")
    return True


# 创建SSE事件格式化函数
def format_sse(data: str, event: str = None) -> str:
    """格式化SSE事件

    Args:
        data: 事件数据
        event: 事件类型 init, progress, file_ready, error

    Returns:
        str: 格式化后的SSE事件
    """
    msg = f"data: {data}\n"
    if event is not None:
        msg = f"event: {event}\n{msg}"
    return f"{msg}\n"


async def track_translation_progress(task_id: str, input_path: str, output_path: str, source_language: str, target_language: str, file_type: str = "word") -> AsyncGenerator[str, None]:
    """跟踪翻译进度并生成SSE事件

    Args:
        task_id: 任务ID
        input_path: 输入文件路径
        output_path: 输出文件路径
        source_language: 源语言代码
        target_language: 目标语言代码
        file_type: 文件类型，支持 "word", "ppt", "excel", "pdf"

    Yields:
        str: SSE事件
    """
    try:
        # 创建一个队列来存储进度更新
        progress_queue = asyncio.Queue()
        
        # 记录上次更新的进度值，用于控制更新频率
        last_recorded_progress = 0

        # 定义进度回调函数
        def progress_callback(progress_data):
            # 更新全局进度字典
            translation_progress[task_id] = progress_data
            # 将进度数据放入队列
            asyncio.create_task(progress_queue.put(progress_data))
            
            # 更新数据库中的翻译进度，每20%更新一次
            nonlocal last_recorded_progress
            progress_value = progress_data.get("progress", 0)
            status = progress_data.get("status")
            
            # 只在以下情况更新数据库：
            # 1. 状态变化（如从processing变为completed）
            # 2. 进度增加了至少20%
            # 3. 进度达到100%
            if status and (
                status != "processing" or 
                progress_value - last_recorded_progress >= 20 or 
                progress_value == 100
            ):
                DocumentTranslationService.update_translation_progress(
                    task_id, 
                    progress_value, 
                    status
                )
                last_recorded_progress = progress_value

        # 注册进度回调
        if file_type == "pdf":
            register_pdf_progress_callback(task_id, progress_callback)
        else:
            register_progress_callback(task_id, progress_callback)

        # 发送开始事件
        progress_data = {
            "status": "started",
            "progress": 0,
            "message": "开始处理文档"
        }
        yield format_sse(json.dumps(progress_data), event="progress")

        # 根据文件类型选择不同的翻译处理函数
        if file_type == "word":
            # 创建一个任务来执行 Word 文档翻译
            translation_task = asyncio.create_task(translate_docx(input_path, output_path, task_id=task_id, source_language=source_language, target_language=target_language))
        elif file_type == "excel":
            # 创建一个任务来执行 Excel 文档翻译
            translation_task = asyncio.create_task(translate_excel(input_path, output_path, task_id=task_id, progress_callback=progress_callback, source_language=source_language, target_language=target_language))
        elif file_type == "pdf":
            # 创建一个任务来执行 PDF 文档翻译
            translation_task = asyncio.create_task(translate_pdf(input_path, output_path, task_id=task_id, progress_callback=progress_callback, source_language=source_language, target_language=target_language))
        else:  # file_type == "ppt"
            # 创建一个任务来执行 PPT 文档翻译
            translation_task = asyncio.create_task(translate_pptx(input_path, output_path, source_language, target_language, progress_callback=progress_callback))

        # 处理进度更新
        while not translation_task.done() or not progress_queue.empty():
            # 尝试从队列中获取进度更新，超时0.5秒
            try:
                progress_data = await asyncio.wait_for(progress_queue.get(), 0.5)
                yield format_sse(json.dumps(progress_data), event="progress")
            except asyncio.TimeoutError:
                # 超时但翻译任务仍在运行，继续等待
                pass

        # 获取翻译结果
        result_message = await translation_task

        # 创建包含文件路径的结果对象
        result_object = {
            "translation_result": result_message,
            "output_path": output_path,
            "output_filename": os.path.basename(output_path)
        }

        logger.info(f"翻译完成，结果对象: {result_object}")

        # 确保发送最终完成事件
        final_progress = translation_progress.get(task_id, {
            "status": "completed",
            "progress": 100,
            "message": "文档翻译完成",
            "result": result_object
        })

        # 确保状态为完成
        if final_progress.get("status") != "failed":
            final_progress["status"] = "completed"
            final_progress["progress"] = 100
            final_progress["message"] = "文档翻译完成"
            final_progress["result"] = result_object
            translation_progress[task_id] = final_progress
            
            # 更新数据库中的翻译状态为完成
            DocumentTranslationService.update_translation_result(
                task_id,
                os.path.basename(output_path)
            )

        logger.info(f"最终进度信息: {final_progress}")

        yield format_sse(json.dumps(final_progress), event="progress")

        # 移除进度回调
        if file_type == "pdf":
            remove_pdf_progress_callback(task_id)
        else:
            remove_progress_callback(task_id)

    except Exception as e:
        # 发送错误事件
        error_data = {
            "status": "failed",
            "progress": 0,
            "message": f"翻译失败: {str(e)}"
        }
        translation_progress[task_id] = error_data
        yield format_sse(json.dumps(error_data), event="error")
        logger.error(f"翻译过程中发生错误: {str(e)}")
        
        # 更新数据库中的翻译错误信息
        DocumentTranslationService.update_translation_error(
            task_id,
            str(e)
        )

        # 移除进度回调
        if file_type == "pdf":
            remove_pdf_progress_callback(task_id)
        else:
            remove_progress_callback(task_id)


@router.post("/api/document/translate")
async def translate_word_document(
    file: UploadFile = File(...),
    source_language: str = Form(""),
    target_language: str = Form(""),
    user_id: str = Form(""),
    api_key_valid: bool = Depends(verify_api_key)
):
    """
    翻译文档接口

    接收上传的Word、Excel、PPT或PDF文档，创建翻译任务，立即返回任务ID
    文档翻译在后台异步进行，不阻塞API响应
    客户端可以通过SSE接口实时获取翻译进度

    Args:
        file: 上传的文档文件（支持Word、Excel、PPT和PDF格式）
        source_language: 源语言代码
        target_language: 目标语言代码，默认为中文
        user_id: 用户ID

    Returns:
        JSONResponse: 包含任务ID的JSON响应
    """
    try:
        logger.info(f"接收到文档翻译请求: {file.filename}, 源语言: {source_language}, 目标语言: {target_language}")

        # 检查文件类型
        if file.filename.endswith(('.docx')):
            file_type = "word"
        elif file.filename.endswith(('.pptx')):
            file_type = "ppt"
        elif file.filename.endswith(('.xlsx')):
            file_type = "excel"
        elif file.filename.endswith('.pdf'):
            file_type = "pdf"
        else:
            raise HTTPException(status_code=400, detail="仅支持Word文档格式 (.docx)、Excel文档格式 (.xlsx)、PPT文档格式 (.pptx) 和PDF文档格式 (.pdf)")
            
        # 使用translate_filename函数翻译文件名
        file.filename = file.filename.replace(" ", "_")
        output_filename = await translate_filename(file.filename, source_language, target_language)
        if file_type == "pdf":
            # 将后缀改为docx
            output_filename = output_filename.replace(".pdf", ".docx")

        # 确保临时目录存在
        os.makedirs("./temp/inputs", exist_ok=True)
        os.makedirs("./temp/outputs", exist_ok=True)

        temp_input_path = f"./temp/inputs/{file.filename}"
        temp_output_path = f"./temp/outputs/{output_filename}"

        # 生成唯一的任务ID
        import uuid
        task_id = str(uuid.uuid4())
        translation_progress[task_id] = {
            "status": "started",
            "progress": 0,
            "message": "开始处理文档"
        }
        
        # 创建数据库记录
        DocumentTranslationService.create_translation_record(
            task_id,
            file.filename,
            source_language,
            target_language,
            user_id,
            task_type="translation"
        )

        try:
            # 保存上传的文件
            with open(temp_input_path, "wb") as temp_file:
                content = await file.read()
                temp_file.write(content)
                
            # 将原始文件上传到FTP服务器（不影响主流程）
            async def upload_original_file():
                success, message, data = await upload_local_file_to_ftp(
                    temp_input_path,
                    file.filename,
                    file.content_type
                )
                if success and data and "imgUrl" in data:
                    # 更新原始文件的FTP URL
                    DocumentTranslationService.update_original_file_ftp_url(task_id, data["imgUrl"])
            
            # 异步上传原始文件
            asyncio.create_task(upload_original_file())

            # 创建后台翻译任务
            async def background_translation_task():
                try:
                    # 根据文件类型选择不同的翻译处理函数，但都使用相同的 track_translation_progress 函数
                    async for _ in track_translation_progress(task_id, temp_input_path, temp_output_path, source_language, target_language, file_type=file_type):
                        pass  # 只需执行翻译，不需要处理SSE事件

                    # 翻译完成后，上传翻译后的文件到FTP服务器
                    if os.path.exists(temp_output_path):
                        # 异步上传翻译后的文件到FTP
                        success, message, data = await upload_local_file_to_ftp(
                            temp_output_path, 
                            output_filename, 
                            file.content_type
                        )
                        if success and data and "imgUrl" in data:
                            # 更新翻译后文件的FTP URL
                            DocumentTranslationService.update_translation_result(
                                task_id,
                                output_filename,
                                data["imgUrl"]
                            )
                        
                        # 获取当前翻译进度信息
                        current_progress = translation_progress.get(task_id, {})
                        
                        # 更新翻译进度信息
                        if "result" not in current_progress:
                            current_progress["result"] = {}

                        current_progress["status"] = "completed"
                        current_progress["progress"] = 100
                        current_progress["message"] = "文档翻译完成"
                        current_progress["result"]["input_path"] = temp_input_path
                        current_progress["result"]["output_path"] = temp_output_path
                        current_progress["result"]["output_filename"] = output_filename
                        translation_progress[task_id] = current_progress

                except Exception as e:
                    # 更新进度为失败
                    translation_progress[task_id] = {
                        "status": "failed",
                        "progress": 0,
                        "message": f"翻译失败: {str(e)}"
                    }
                    
                    # 更新数据库中的翻译错误信息
                    DocumentTranslationService.update_translation_error(
                        task_id,
                        str(e)
                    )
                    logger.error(f"后台翻译任务失败: {str(e)}")

            # 启动后台翻译任务
            asyncio.create_task(background_translation_task())

            # 立即返回任务ID和状态
            return JSONResponse(
                content={
                    "code": 0,
                    "message": "success",
                    "data": {
                        "task_id": task_id,
                        "status": "processing",
                        "filename": file.filename,
                        "output_filename": output_filename
                    }
                }
            )

        except Exception as e:
            # 更新进度为失败
            translation_progress[task_id] = {
                "status": "failed",
                "progress": 0,
                "message": f"翻译失败: {str(e)}"
            }
            
            # 更新数据库中的翻译错误信息
            DocumentTranslationService.update_translation_error(
                task_id,
                str(e)
            )

            raise HTTPException(status_code=500, detail=f"翻译失败: {str(e)}")
    except Exception as e:
        logger.error(f"文档翻译失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文档翻译失败: {str(e)}")
    
async def cleanup_temp_files(input_path: str, output_path: str, task_id: str):
    """
    清理临时文件的后台任务
    """
    try:
        # 等待更长时间，确保用户有足够时间下载文件
        logger.info(f"计划在 60 秒后清理临时文件: {input_path}, {output_path}")
        await asyncio.sleep(60)  # 1分钟

        # 清理输入文件
        if input_path and os.path.exists(input_path):
            try:
                os.unlink(input_path)
                logger.info(f"已清理输入文件: {input_path}")
            except Exception as e:
                logger.error(f"清理输入文件失败: {input_path}, 错误: {str(e)}")

        # 清理输出文件
        if output_path and os.path.exists(output_path):
            try:
                os.unlink(output_path)
                logger.info(f"已清理输出文件: {output_path}")
            except Exception as e:
                logger.error(f"清理输出文件失败: {output_path}, 错误: {str(e)}")

        # 清理进度信息
        if task_id in translation_progress:
            del translation_progress[task_id]
            logger.info(f"已清理任务进度信息: {task_id}")

    except Exception as e:
        logger.error(f"清理临时文件失败: {str(e)}")

@router.get("/api/document/translate/download/{task_id}")
async def download_translated_document(task_id: str):
    """
    下载翻译后的文档
    
    Args:
        task_id: 翻译任务ID
        
    Returns:
        FileResponse: 翻译后的文档文件
    """
    try:
        logger.info(f"接收到下载翻译文档请求，任务ID: {task_id}")
        
        # 从数据库获取翻译记录
        record = DocumentTranslationService.get_translation_record(task_id)
        if not record:
            logger.error(f"未找到翻译任务记录，任务ID: {task_id}")
            raise HTTPException(status_code=404, detail="未找到翻译任务记录")
        
        # 检查翻译状态
        if record["translation_status"] != "completed":
            logger.error(f"翻译任务未完成，任务ID: {task_id}, 状态: {record['translation_status']}")
            raise HTTPException(status_code=400, detail="翻译任务未完成")
        
        # 获取FTP URL和文件名
        ftp_url = record.get("translated_file_ftp_url")
        output_filename = record.get("translated_filename")
        
        # 从FTP下载文件
        logger.info(f"从FTP下载文件，URL: {ftp_url}")
        
        # 创建临时文件
        temp_dir = "./temp/downloads"
        os.makedirs(temp_dir, exist_ok=True)
        temp_file_path = f"{temp_dir}/{output_filename}"
        
        # 下载文件
        async with aiohttp.ClientSession() as session:
            async with session.get(ftp_url) as response:
                if response.status != 200:
                    logger.error(f"从FTP下载文件失败，状态码: {response.status}, URL: {ftp_url}")
                    raise HTTPException(status_code=500, detail="从FTP下载文件失败")
                
                # 保存文件
                content = await response.read()
                with open(temp_file_path, "wb") as f:
                    f.write(content)
        
        logger.info(f"文件下载成功，保存至: {temp_file_path}")
        # 清理临时文件
        asyncio.create_task(cleanup_temp_files(temp_file_path, temp_file_path, task_id))
        asyncio.create_task(cleanup_temp_files(f"./temp/inputs/{record['original_filename']}", f"./temp/outputs/{record['translated_filename']}", task_id))

        # 根据文件类型设置正确的MIME类型
        if record['original_filename'].endswith(('.docx')):
            media_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        elif record['original_filename'].endswith(('.xlsx')):
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        elif record['original_filename'].endswith('.pdf'):
            media_type = "application/pdf"
        else:  # PPT文件
            media_type = "application/vnd.openxmlformats-officedocument.presentationml.presentation"

        # 返回文件
        return FileResponse(
            path=temp_file_path,
            filename=output_filename,
            media_type=media_type
        )
    except Exception as e:
        logger.error(f"下载翻译文档失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载翻译文档失败: {str(e)}")

@router.get("/api/document/translate/history")
async def get_document_translation_history(
    user_id: str,
    status: Optional[str] = None,
    task_type: Optional[str] = None,
    page: int = 1,
    page_size: int = 5,
    token: bool = Depends(verify_api_key)
):
    """
    获取文档翻译历史记录
    
    Args:
        user_id: 用户ID
        status: 翻译状态，可选 (pending-等待中, processing-处理中, completed-已完成, failed-失败)
        task_type: 任务类型，可选 (translation-翻译, conversion-转换)
        page: 页码，默认为1
        page_size: 每页记录数，默认为5
        
    Returns:
        JSONResponse: 包含翻译历史记录和分页信息的JSON响应
    """
    try:
        logger.info(f"接收到查询文档翻译历史记录请求，用户ID: {user_id}, 状态: {status}, 任务类型: {task_type}, 页码: {page}, 每页记录数: {page_size}")
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询翻译记录
        records, total_count = DocumentTranslationService.get_translation_records(
            user_id=user_id,
            status=status,
            task_type=task_type,
            limit=page_size,
            offset=offset
        )
        
        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size
        
        # 构建响应数据
        response_data = {
            "code": 0,
            "message": "success",
            "data": {
                "records": records,
                "pagination": {
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "current_page": page,
                    "page_size": page_size,
                }
            }
        }
        
        return JSONResponse(content=response_data)
    except Exception as e:
        logger.error(f"查询文档翻译历史记录失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"查询文档翻译历史记录失败: {str(e)}"}
        )

@router.get("/api/document/translate/progress/{task_id}")
async def get_document_translation_progress(
    task_id: str,
    api_key_valid: bool = Depends(verify_api_key)
):
    """
    获取文档翻译进度
    
    Args:
        task_id: 翻译任务ID
        
    Returns:
        JSONResponse: 包含翻译进度信息的JSON响应
    """
    try:
        logger.info(f"接收到查询文档翻译进度请求，任务ID: {task_id}")
        
        # 从内存中获取翻译进度
        progress_data = translation_progress.get(task_id, {
            "status": "unknown",
            "progress": 0,
            "message": "未找到翻译任务"
        })
        
        # 如果内存中没有进度信息，尝试从数据库查询
        if progress_data.get("status") == "unknown":
            record = DocumentTranslationService.get_translation_record(task_id)
            if record:
                progress_data = {
                    "status": record.get("translation_status", "unknown"),
                    "progress": record.get("translation_progress", 0),
                    "message": record.get("error_message", "从数据库获取的任务信息")
                }
                
                # 如果状态是completed，添加下载链接
                if record.get("translation_status") == "completed":
                    progress_data["message"] = f"/document/translate/download/{task_id}"
        
        # 构建响应数据
        response_data = {
            "code": 0,
            "message": "success",
            "data": progress_data
        }
        
        logger.info(f"查询文档翻译进度成功: {response_data}")
        return JSONResponse(content=response_data)
    except Exception as e:
        logger.error(f"查询文档翻译进度失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"查询文档翻译进度失败: {str(e)}"}
        )

# --- Word转PDF处理函数 ---
async def convert_word_to_pdf(input_docx_path: str, output_pdf_path: str, progress_callback=None) -> str:
    """
    将Word文档转换为PDF文档
    
    Args:
        input_docx_path: 输入Word文档路径
        output_pdf_path: 输出PDF文档路径
        progress_callback: 进度回调函数
        
    Returns:
        str: 转换完成状态信息
    """
    # 更新进度的辅助函数
    def update_progress(progress, message, status="processing"):
        if progress_callback:
            progress_callback({
                "status": status,
                "progress": progress,
                "message": message
            })
        logger.info(f"[{progress}%] {message}")
    
    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_docx_path):
            error_msg = f"输入文件不存在: {input_docx_path}"
            update_progress(0, error_msg, status="failed")
            return f"错误: {error_msg}"
        
        update_progress(20, "开始Word转PDF转换")
        
        # 使用libreoffice进行转换
        import asyncio
        from subprocess import run
        output_dir = os.path.dirname(output_pdf_path)
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        update_progress(40, "正在执行文档转换")
        
        conversion_cmd = [
            settings.LIBREOFFICE_NAME, "--invisible", "--headless", "--convert-to", "pdf:writer_pdf_Export", 
            input_docx_path, "--outdir", output_dir
        ]
        print(conversion_cmd)
        # 执行命令
        proc = await asyncio.create_subprocess_exec(
            *conversion_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await proc.communicate()
        
        if proc.returncode != 0:
            error_msg = f"Word转PDF失败: {stderr.decode()}"
            update_progress(0, error_msg, status="failed")
            return f"错误: {error_msg}"
        
        update_progress(80, "处理输出文件")
        
        # 获取转换后的PDF文件名
        converted_pdf_name = os.path.basename(input_docx_path).replace('.docx', '.pdf')
        converted_pdf_path = os.path.join(output_dir, converted_pdf_name)
        
        # 如果输出路径与转换后的文件路径不同，需要重命名
        if converted_pdf_path != output_pdf_path:
            os.rename(converted_pdf_path, output_pdf_path)
        
        update_progress(100, "Word转PDF完成", status="completed")
        return "Word转PDF成功完成"
        
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        error_msg = f"Word转PDF过程中发生错误: {str(e)}"
        update_progress(0, error_msg, status="failed")
        logger.error(error_msg)
        logger.error(error_trace)
        return f"错误: {str(e)}"

async def track_word_to_pdf_progress(task_id: str, input_path: str, output_path: str) -> AsyncGenerator[str, None]:
    """跟踪Word转PDF进度并生成SSE事件

    Args:
        task_id: 任务ID
        input_path: 输入Word文件路径
        output_path: 输出PDF文件路径

    Yields:
        str: SSE事件
    """
    try:
        # 创建一个队列来存储进度更新
        progress_queue = asyncio.Queue()
        
        # 记录上次更新的进度值，用于控制更新频率
        last_recorded_progress = 0

        # 定义进度回调函数
        def progress_callback(progress_data):
            # 更新全局进度字典
            translation_progress[task_id] = progress_data
            # 将进度数据放入队列
            asyncio.create_task(progress_queue.put(progress_data))
            
            # 更新数据库中的转换进度，每20%更新一次
            nonlocal last_recorded_progress
            progress_value = progress_data.get("progress", 0)
            status = progress_data.get("status")
            
            # 只在以下情况更新数据库：
            # 1. 状态变化（如从processing变为completed）
            # 2. 进度增加了至少20%
            # 3. 进度达到100%
            if status and (
                status != "processing" or 
                progress_value - last_recorded_progress >= 20 or 
                progress_value == 100
            ):
                DocumentTranslationService.update_translation_progress(
                    task_id, 
                    progress_value, 
                    status
                )
                last_recorded_progress = progress_value

        # 发送开始事件
        progress_data = {
            "status": "started",
            "progress": 10,
            "message": "开始Word转PDF转换"
        }
        yield format_sse(json.dumps(progress_data), event="progress")

        # 创建一个任务来执行Word转PDF转换
        conversion_task = asyncio.create_task(convert_word_to_pdf(input_path, output_path, progress_callback))

        # 处理进度更新
        while not conversion_task.done() or not progress_queue.empty():
            # 尝试从队列中获取进度更新，超时0.5秒
            try:
                progress_data = await asyncio.wait_for(progress_queue.get(), 0.5)
                yield format_sse(json.dumps(progress_data), event="progress")
            except asyncio.TimeoutError:
                # 超时但转换任务仍在运行，继续等待
                pass

        # 获取转换结果
        result_message = await conversion_task

        # 创建包含文件路径的结果对象
        result_object = {
            "conversion_result": result_message,
            "output_path": output_path,
            "output_filename": os.path.basename(output_path)
        }

        logger.info(f"Word转PDF完成，结果对象: {result_object}")

        # 确保发送最终完成事件
        final_progress = translation_progress.get(task_id, {
            "status": "completed",
            "progress": 100,
            "message": "Word转PDF完成",
            "result": result_object
        })

        # 确保状态为完成
        if final_progress.get("status") != "failed":
            final_progress["status"] = "completed"
            final_progress["progress"] = 100
            final_progress["message"] = "Word转PDF完成"
            final_progress["result"] = result_object
            translation_progress[task_id] = final_progress
            
            # 更新数据库中的转换状态为完成
            DocumentTranslationService.update_translation_result(
                task_id,
                os.path.basename(output_path)
            )

        logger.info(f"最终进度信息: {final_progress}")

        yield format_sse(json.dumps(final_progress), event="progress")

    except Exception as e:
        # 发送错误事件
        error_data = {
            "status": "failed",
            "progress": 0,
            "message": f"Word转PDF失败: {str(e)}"
        }
        translation_progress[task_id] = error_data
        yield format_sse(json.dumps(error_data), event="error")
        logger.error(f"Word转PDF过程中发生错误: {str(e)}")
        
        # 更新数据库中的转换错误信息
        DocumentTranslationService.update_translation_error(
            task_id,
            str(e)
        )

@router.post("/api/document/word-to-pdf")
async def convert_word_to_pdf_api(
    file: UploadFile = File(...),
    user_id: str = Form(""),
    api_key_valid: bool = Depends(verify_api_key)
):
    """
    Word转PDF接口
    
    Args:
        file: 上传的Word文档
        user_id: 用户ID（可选）
        api_key_valid: API密钥验证
        
    Returns:
        JSONResponse: 包含任务ID的JSON响应
    """
    import uuid
    
    # 生成唯一的任务ID
    task_id = str(uuid.uuid4())
    
    try:
        logger.info(f"接收到Word转PDF请求: {file.filename}, 用户ID: {user_id}")
        
        # 验证文件类型
        if not file.filename.lower().endswith(('.docx')):
            return JSONResponse(
                status_code=400,
                content={"code": 400, "message": "不支持的文件格式，请上传Word文档（.docx）"}
            )
        
        file.filename = file.filename.replace(" ", "_")
        # 确保临时目录存在
        os.makedirs("./temp/inputs", exist_ok=True)
        os.makedirs("./temp/outputs", exist_ok=True)
        
        # 生成文件路径
        input_filename = f"{task_id}_{file.filename}"
        input_path = f"./temp/inputs/{input_filename}"
        
        output_filename = f"{task_id}_{os.path.splitext(file.filename)[0]}.pdf"
        output_path = f"./temp/outputs/{output_filename}"
        
        # 初始化任务进度
        translation_progress[task_id] = {
            "status": "started",
            "progress": 0,
            "message": "开始Word转PDF转换"
        }
        
        # 创建数据库记录 - Word转PDF使用相同的表结构
        DocumentTranslationService.create_translation_record(
            task_id,
            file.filename,
            "word",  # 源格式为word
            "pdf",   # 目标格式为pdf
            user_id,
            task_type="conversion"
        )
        
        try:
            # 保存上传的文件
            with open(input_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            
            logger.info(f"Word文件已保存到: {input_path}")
            
            # 将原始文件上传到FTP服务器（不影响主流程）
            async def upload_original_file():
                success, message, data = await upload_local_file_to_ftp(
                    input_path,
                    file.filename,
                    file.content_type
                )
                if success and data and "imgUrl" in data:
                    # 更新原始文件的FTP URL
                    DocumentTranslationService.update_original_file_ftp_url(task_id, data["imgUrl"])
            
            # 异步上传原始文件
            asyncio.create_task(upload_original_file())
            
            # 创建后台转换任务
            async def background_conversion_task():
                try:
                    # 执行Word转PDF转换
                    async for _ in track_word_to_pdf_progress(task_id, input_path, output_path):
                        pass  # 只需执行转换，不需要处理SSE事件
                    
                    # 转换完成后，更新进度信息
                    if os.path.exists(output_path):
                        # 异步上传转换后的文件到FTP
                        success, message, data = await upload_local_file_to_ftp(
                            output_path, 
                            output_filename, 
                            "application/pdf"
                        )
                        if success and data and "imgUrl" in data:
                            # 更新转换后文件的FTP URL
                            DocumentTranslationService.update_translation_result(
                                task_id,
                                output_filename,
                                data["imgUrl"]
                            )
                        
                            # 获取当前转换进度信息
                            current_progress = translation_progress.get(task_id, {})
                            
                            # 更新转换进度信息
                            if "result" not in current_progress:
                                current_progress["result"] = {}

                            current_progress["status"] = "completed"
                            current_progress["progress"] = 100
                            current_progress["message"] = "Word转PDF完成"
                            current_progress["result"]["input_path"] = input_path
                            current_progress["result"]["output_path"] = output_path
                            current_progress["result"]["output_filename"] = output_filename
                            translation_progress[task_id] = current_progress
                            
                            # 更新数据库中的转换状态为完成
                            DocumentTranslationService.update_translation_result(
                                task_id,
                                output_filename
                            )
                            
                            logger.info(f"Word转PDF转换完成，任务ID: {task_id}")
                        
                except Exception as e:
                    # 更新进度为失败
                    translation_progress[task_id] = {
                        "status": "failed",
                        "progress": 0,
                        "message": f"Word转PDF失败: {str(e)}"
                    }
                    
                    # 更新数据库中的转换错误信息
                    DocumentTranslationService.update_translation_error(
                        task_id,
                        str(e)
                    )
                    
                    logger.error(f"后台Word转PDF任务失败: {str(e)}")
            
            # 启动后台转换任务
            asyncio.create_task(background_conversion_task())
            # 启动后台清理任务
            asyncio.create_task(cleanup_temp_files(input_path, output_path, None))

            # 立即返回任务ID和状态
            return JSONResponse(
                content={
                    "code": 0,
                    "message": "success",
                    "data": {
                        "task_id": task_id,
                        "status": "processing",
                        "filename": file.filename,
                        "output_filename": output_filename
                    }
                }
            )
            
        except Exception as e:
            # 更新进度为失败
            translation_progress[task_id] = {
                "status": "failed",
                "progress": 0,
                "message": f"Word转PDF失败: {str(e)}"
            }
            
            # 更新数据库中的转换错误信息
            DocumentTranslationService.update_translation_error(
                task_id,
                str(e)
            )
            
            raise HTTPException(status_code=500, detail=f"Word转PDF失败: {str(e)}")
    except Exception as e:
        logger.error(f"Word转PDF接口错误: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"Word转PDF失败: {str(e)}"}
        )

@router.get("/api/document/word-to-pdf/download/{task_id}")
async def download_converted_pdf(task_id: str):
    """
    下载转换后的PDF文档
    
    Args:
        task_id: 任务ID
        
    Returns:
        FileResponse: 转换后的PDF文件
    """
    try:
        logger.info(f"接收到下载转换后PDF文档请求，任务ID: {task_id}")
        
        # 从数据库获取转换记录
        record = DocumentTranslationService.get_translation_record(task_id)
        if not record:
            logger.error(f"未找到转换任务记录，任务ID: {task_id}")
            raise HTTPException(status_code=404, detail="未找到转换任务记录")
        
        # 检查转换状态
        if record["translation_status"] != "completed":
            logger.error(f"转换任务未完成，任务ID: {task_id}, 状态: {record['translation_status']}")
            raise HTTPException(status_code=400, detail="转换任务未完成")
        
        # 获取FTP URL和文件名
        ftp_url = record.get("translated_file_ftp_url")
        output_filename = record.get("translated_filename")
        
        # 从FTP下载文件
        logger.info(f"从FTP下载文件，URL: {ftp_url}")
        
        # 创建临时文件
        temp_dir = "./temp/downloads"
        os.makedirs(temp_dir, exist_ok=True)
        temp_file_path = f"{temp_dir}/{output_filename}"
        
        # 下载文件
        async with aiohttp.ClientSession() as session:
            async with session.get(ftp_url) as response:
                if response.status != 200:
                    logger.error(f"从FTP下载文件失败，状态码: {response.status}, URL: {ftp_url}")
                    raise HTTPException(status_code=500, detail="从FTP下载文件失败")
                
                # 保存文件
                content = await response.read()
                with open(temp_file_path, "wb") as f:
                    f.write(content)
        
        logger.info(f"文件下载成功，保存至: {temp_file_path}")
        
        # 清理临时文件
        asyncio.create_task(cleanup_temp_files(temp_file_path, temp_file_path, task_id))

        # 返回文件
        return FileResponse(
            path=temp_file_path,
            filename=output_filename,
            media_type="application/pdf"
        )
        
    except Exception as e:
        logger.error(f"下载转换后PDF文件失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"下载失败: {str(e)}"}
        )

@router.get("/api/document/word-to-pdf/progress/{task_id}")
async def get_word_to_pdf_progress(
    task_id: str,
    api_key_valid: bool = Depends(verify_api_key)
):
    """
    查询Word转PDF进度
    
    Args:
        task_id: 任务ID
        api_key_valid: API密钥验证
        
    Returns:
        JSONResponse: 进度信息
    """
    try:
        logger.info(f"接收到查询Word转PDF进度请求，任务ID: {task_id}")
        
        # 从内存中获取转换进度
        progress_data = translation_progress.get(task_id, {
            "status": "processing",
            "progress": 10,
            "message": "转换任务进行中"
        })
        
        # 如果内存中没有进度信息，尝试从数据库查询
        if progress_data.get("status") == "processing" and progress_data.get("progress") == 10:
            record = DocumentTranslationService.get_translation_record(task_id)
            if record:
                progress_data = {
                    "status": record.get("translation_status", "processing"),
                    "progress": record.get("translation_progress", 10),
                    "message": record.get("error_message", "从数据库获取的任务信息")
                }
                
                # 如果状态是completed，添加下载链接
                if record.get("translation_status") == "completed":
                    progress_data["message"] = f"/api/document/word-to-pdf/download/{task_id}"
        
        # 如果状态是completed，添加下载链接信息
        if progress_data.get("status") == "completed":
            progress_data["message"] = f"/api/document/word-to-pdf/download/{task_id}"
        
        # 构建响应数据
        response_data = {
            "code": 0,
            "message": "success",
            "data": progress_data
        }
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"查询Word转PDF进度失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"查询Word转PDF进度失败: {str(e)}"}
        )

# --- PDF转Word处理函数 ---
async def convert_pdf_to_word(input_pdf_path: str, output_docx_path: str, progress_callback=None) -> str:
    """
    将PDF文档转换为Word文档

    Args:
        input_pdf_path: 输入PDF文档路径
        output_docx_path: 输出Word文档路径
        progress_callback: 进度回调函数

    Returns:
        str: 转换完成状态信息
    """
    # 更新进度的辅助函数
    def update_progress(progress, message, status="processing"):
        if progress_callback:
            progress_callback({
                "status": status,
                "progress": progress,
                "message": message
            })
        logger.info(f"[{progress}%] {message}")

    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_pdf_path):
            error_msg = f"输入文件不存在: {input_pdf_path}"
            update_progress(0, error_msg, status="failed")
            return f"错误: {error_msg}"

        update_progress(20, "开始PDF转Word转换")

        # 使用pdf2docx进行转换
        from pdf2docx import Converter

        # 确保输出目录存在
        output_dir = os.path.dirname(output_docx_path)
        os.makedirs(output_dir, exist_ok=True)

        update_progress(40, "正在执行文档转换")

        # 在执行器中运行阻塞操作
        loop = asyncio.get_running_loop()

        def _convert():
            # 创建转换器
            cv = Converter(input_pdf_path)
            # 执行转换
            cv.convert(output_docx_path)
            # 关闭转换器
            cv.close()
            return True

        # 在执行器中运行阻塞操作
        result = await loop.run_in_executor(None, _convert)

        if not result:
            error_msg = f"PDF转Word失败: 转换过程出错"
            update_progress(0, error_msg, status="failed")
            return f"错误: {error_msg}"

        update_progress(80, "处理输出文件")

        # 检查输出文件是否生成成功
        if not os.path.exists(output_docx_path):
            error_msg = f"PDF转Word失败: 输出文件未生成"
            update_progress(0, error_msg, status="failed")
            return f"错误: {error_msg}"

        update_progress(100, "PDF转Word完成", status="completed")
        return "PDF转Word成功完成"

    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        error_msg = f"PDF转Word过程中发生错误: {str(e)}"
        update_progress(0, error_msg, status="failed")
        logger.error(error_msg)
        logger.error(error_trace)
        return f"错误: {str(e)}"

async def track_pdf_to_word_progress(task_id: str, input_path: str, output_path: str) -> AsyncGenerator[str, None]:
    """跟踪PDF转Word进度并生成SSE事件

    Args:
        task_id: 任务ID
        input_path: 输入PDF文件路径
        output_path: 输出Word文件路径

    Yields:
        str: SSE事件
    """
    try:
        # 创建一个队列来存储进度更新
        progress_queue = asyncio.Queue()

        # 记录上次更新的进度值，用于控制更新频率
        last_recorded_progress = 0

        # 定义进度回调函数
        def progress_callback(progress_data):
            # 更新全局进度字典
            translation_progress[task_id] = progress_data
            # 将进度数据放入队列
            asyncio.create_task(progress_queue.put(progress_data))

            # 更新数据库中的转换进度，每20%更新一次
            nonlocal last_recorded_progress
            progress_value = progress_data.get("progress", 0)
            status = progress_data.get("status")

            # 只在以下情况更新数据库：
            # 1. 状态变化（如从processing变为completed）
            # 2. 进度增加了至少20%
            # 3. 进度达到100%
            if status and (
                status != "processing" or
                progress_value - last_recorded_progress >= 20 or
                progress_value == 100
            ):
                DocumentTranslationService.update_translation_progress(
                    task_id,
                    progress_value,
                    status
                )
                last_recorded_progress = progress_value

        # 发送开始事件
        progress_data = {
            "status": "started",
            "progress": 10,
            "message": "开始PDF转Word转换"
        }
        yield format_sse(json.dumps(progress_data), event="progress")

        # 创建一个任务来执行PDF转Word转换
        conversion_task = asyncio.create_task(convert_pdf_to_word(input_path, output_path, progress_callback))

        # 处理进度更新
        while not conversion_task.done() or not progress_queue.empty():
            # 尝试从队列中获取进度更新，超时0.5秒
            try:
                progress_data = await asyncio.wait_for(progress_queue.get(), 0.5)
                yield format_sse(json.dumps(progress_data), event="progress")
            except asyncio.TimeoutError:
                # 超时但转换任务仍在运行，继续等待
                pass

        # 获取转换结果
        result_message = await conversion_task

        # 创建包含文件路径的结果对象
        result_object = {
            "conversion_result": result_message,
            "output_path": output_path,
            "output_filename": os.path.basename(output_path)
        }

        # 发送完成事件
        final_data = {
            "status": "completed",
            "progress": 100,
            "message": f"/api/document/pdf-to-word/download/{task_id}",
            "result": result_object
        }
        translation_progress[task_id] = final_data
        yield format_sse(json.dumps(final_data), event="completed")

        # 更新数据库中的转换完成状态
        DocumentTranslationService.update_translation_progress(
            task_id,
            100,
            "completed"
        )

    except Exception as e:
        # 发送错误事件
        error_data = {
            "status": "failed",
            "progress": 0,
            "message": f"PDF转Word失败: {str(e)}"
        }
        translation_progress[task_id] = error_data
        yield format_sse(json.dumps(error_data), event="error")
        logger.error(f"PDF转Word过程中发生错误: {str(e)}")

        # 更新数据库中的转换错误信息
        DocumentTranslationService.update_translation_error(
            task_id,
            str(e)
        )

@router.post("/api/document/pdf-to-word")
async def convert_pdf_to_word_api(
    file: UploadFile = File(...),
    user_id: str = Form(""),
    api_key_valid: bool = Depends(verify_api_key)
):
    """
    PDF转Word接口

    Args:
        file: 上传的PDF文档
        user_id: 用户ID（可选）
        api_key_valid: API密钥验证

    Returns:
        JSONResponse: 包含任务ID的JSON响应
    """
    import uuid

    # 生成唯一的任务ID
    task_id = str(uuid.uuid4())

    try:
        logger.info(f"接收到PDF转Word请求: {file.filename}, 用户ID: {user_id}")

        # 验证文件类型
        if not file.filename.lower().endswith(('.pdf')):
            return JSONResponse(
                status_code=400,
                content={"code": 400, "message": "不支持的文件格式，请上传PDF文档（.pdf）"}
            )

        file.filename = file.filename.replace(" ", "_")
        # 确保临时目录存在
        os.makedirs("./temp/inputs", exist_ok=True)
        os.makedirs("./temp/outputs", exist_ok=True)

        # 生成文件路径
        input_filename = f"{task_id}_{file.filename}"
        input_path = f"./temp/inputs/{input_filename}"

        output_filename = f"{task_id}_{os.path.splitext(file.filename)[0]}.docx"
        output_path = f"./temp/outputs/{output_filename}"

        # 初始化任务进度
        translation_progress[task_id] = {
            "status": "started",
            "progress": 0,
            "message": "开始PDF转Word转换"
        }

        # 保存上传的文件
        with open(input_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        logger.info(f"文件保存成功: {input_path}")

        # 在数据库中创建转换记录
        try:
            DocumentTranslationService.create_translation_record(
                task_id,
                file.filename,
                "pdf",
                "word",
                user_id,
                task_type="conversion"
            )
            logger.info(f"数据库记录创建成功，任务ID: {task_id}")
        except Exception as db_error:
            logger.warning(f"创建数据库记录失败，但继续处理: {str(db_error)}")

        # 启动后台转换任务
        try:
            # 将原始文件上传到FTP服务器（不影响主流程）
            async def upload_original_file():
                success, message, data = await upload_local_file_to_ftp(
                    input_path,
                    file.filename,
                    file.content_type
                )
                if success and data and "imgUrl" in data:
                    # 更新原始文件的FTP URL
                    DocumentTranslationService.update_original_file_ftp_url(task_id, data["imgUrl"])
            
            # 异步上传原始文件
            asyncio.create_task(upload_original_file())

            # 创建后台转换任务
            async def background_conversion_task():
                try:
                    # 执行转换
                    async for _ in track_pdf_to_word_progress(task_id, input_path, output_path):
                        pass  # 进度更新已在track_pdf_to_word_progress中处理

                    logger.info(f"PDF转Word转换完成，任务ID: {task_id}")

                    # 更新数据库中的转换完成状态
                    # 转换完成后，更新进度信息
                    if os.path.exists(output_path):
                        # 异步上传转换后的文件到FTP
                        success, message, data = await upload_local_file_to_ftp(
                            output_path, 
                            output_filename, 
                            "application/pdf"
                        )
                        if success and data and "imgUrl" in data:
                            # 更新转换后文件的FTP URL
                            DocumentTranslationService.update_translation_result(
                                task_id,
                                output_filename,
                                data["imgUrl"]
                            )

                        # 更新数据库中的转换状态为完成
                        DocumentTranslationService.update_translation_result(
                            task_id,
                            output_filename
                        )
                except Exception as e:
                    # 更新进度为失败
                    translation_progress[task_id] = {
                        "status": "failed",
                        "progress": 0,
                        "message": f"PDF转Word失败: {str(e)}"
                    }

                    # 更新数据库中的转换错误信息
                    DocumentTranslationService.update_translation_error(
                        task_id,
                        str(e)
                    )

                    logger.error(f"后台PDF转Word任务失败: {str(e)}")

            # 启动后台转换任务
            asyncio.create_task(background_conversion_task())
            # 启动后台清理任务
            asyncio.create_task(cleanup_temp_files(input_path, output_path, None))

            logger.info(f"PDF转Word后台任务启动成功，任务ID: {task_id}")

        except Exception as e:
            # 更新进度为失败
            translation_progress[task_id] = {
                "status": "failed",
                "progress": 0,
                "message": f"PDF转Word失败: {str(e)}"
            }

            # 更新数据库中的转换错误信息
            DocumentTranslationService.update_translation_error(
                task_id,
                str(e)
            )

            raise HTTPException(status_code=500, detail=f"PDF转Word失败: {str(e)}")

        # 返回任务ID
        response_data = {
            "code": 0,
            "message": "success",
            "data": {
                "task_id": task_id,
                "status": "processing",
                "filename": file.filename,
                "output_filename": output_filename
            }
        }

        logger.info(f"PDF转Word任务创建成功: {response_data}")
        return JSONResponse(content=response_data)

    except Exception as e:
        logger.error(f"PDF转Word接口错误: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"PDF转Word失败: {str(e)}"}
        )

@router.get("/api/document/pdf-to-word/progress/{task_id}")
async def get_pdf_to_word_progress(
    task_id: str,
    api_key_valid: bool = Depends(verify_api_key)
):
    """
    查询PDF转Word进度

    Args:
        task_id: 任务ID
        api_key_valid: API密钥验证

    Returns:
        JSONResponse: 进度信息
    """
    try:
        logger.info(f"接收到查询PDF转Word进度请求，任务ID: {task_id}")

        # 从内存中获取转换进度
        progress_data = translation_progress.get(task_id, {
            "status": "processing",
            "progress": 10,
            "message": "转换任务进行中"
        })

        # 如果内存中没有进度信息，尝试从数据库查询
        if progress_data.get("status") == "processing" and progress_data.get("progress") == 10:
            record = DocumentTranslationService.get_translation_record(task_id)
            if record:
                progress_data = {
                    "status": record.get("translation_status", "processing"),
                    "progress": record.get("translation_progress", 10),
                    "message": record.get("error_message", "从数据库获取的任务信息")
                }

                # 如果状态是completed，添加下载链接
                if record.get("translation_status") == "completed":
                    progress_data["message"] = f"/api/document/pdf-to-word/download/{task_id}"

        # 如果状态是completed，添加下载链接信息
        if progress_data.get("status") == "completed":
            progress_data["message"] = f"/api/document/pdf-to-word/download/{task_id}"

        # 构建响应数据
        response_data = {
            "code": 0,
            "message": "success",
            "data": progress_data
        }

        return JSONResponse(content=response_data)

    except Exception as e:
        logger.error(f"查询PDF转Word进度失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"查询PDF转Word进度失败: {str(e)}"}
        )

@router.get("/api/document/pdf-to-word/download/{task_id}")
async def download_converted_word(task_id: str):
    """
    下载转换后的Word文档

    Args:
        task_id: 任务ID

    Returns:
        FileResponse: Word文档文件
    """
    try:
        logger.info(f"接收到下载转换后Word文档请求，任务ID: {task_id}")

        # 从数据库获取转换记录
        record = DocumentTranslationService.get_translation_record(task_id)
        if not record:
            logger.error(f"未找到转换任务记录，任务ID: {task_id}")
            raise HTTPException(status_code=404, detail="未找到转换任务记录")

        # 检查转换状态
        if record["translation_status"] != "completed":
            logger.error(f"转换任务未完成，任务ID: {task_id}, 状态: {record['translation_status']}")
            raise HTTPException(status_code=400, detail="转换任务未完成")

        # 获取FTP URL和文件名
        ftp_url = record.get("translated_file_ftp_url")
        output_filename = record.get("translated_filename")
        
        # 从FTP下载文件
        logger.info(f"从FTP下载文件，URL: {ftp_url}")
        
        # 创建临时文件
        temp_dir = "./temp/downloads"
        os.makedirs(temp_dir, exist_ok=True)
        temp_file_path = f"{temp_dir}/{output_filename}"
        
        # 下载文件
        async with aiohttp.ClientSession() as session:
            async with session.get(ftp_url) as response:
                if response.status != 200:
                    logger.error(f"从FTP下载文件失败，状态码: {response.status}, URL: {ftp_url}")
                    raise HTTPException(status_code=500, detail="从FTP下载文件失败")
                
                # 保存文件
                content = await response.read()
                with open(temp_file_path, "wb") as f:
                    f.write(content)
        
        logger.info(f"文件下载成功，保存至: {temp_file_path}")
        
        # 清理临时文件
        asyncio.create_task(cleanup_temp_files(temp_file_path, temp_file_path, task_id))

        # 返回文件
        return FileResponse(
            path=temp_file_path,
            filename=output_filename,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )

    except Exception as e:
        logger.error(f"下载转换后Word文件失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"下载失败: {str(e)}"}
        )