from loguru import logger
from urllib.parse import unquote
from fastapi import APIRouter, HTTPException, Depends, Response

from app.core.config import settings
from app.utils.wechat_crypto import WXBizMsgCrypt
from app.services.wechat_service import WeChatService
from app.services.mengchat_service import MengChatService


router = APIRouter()
wechat_service = WeChatService()
mengchat_service = MengChatService()

def get_crypto() -> WXBizMsgCrypt:
    """获取企业微信加密类实例"""
    try:
        return WXBizMsgCrypt(
            token=settings.TOKEN,
            encoding_aes_key=settings.ENCODING_AES_KEY,
            corp_id=settings.CORP_ID
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to initialize WXBizMsgCrypt: {str(e)}")


@router.get("/auth")
async def auth(
    msg_signature: str,
    timestamp: str,
    nonce: str,
    echostr: str,
    crypto: WXBizMsgCrypt = Depends(get_crypto)
):
    """企业微信服务器验证接口
    
    Args:
        msg_signature: 企业微信加密签名
        timestamp: 时间戳
        nonce: 随机数
        echostr: 加密的随机字符串
        crypto: 加密工具实例
    
    Returns:
        str: 解密后的echostr
    """
    try:
        # 验证签名并解密
        decrypted_str = crypto.verify_url(msg_signature, timestamp, nonce, echostr)
        return decrypted_str
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Verification failed: {str(e)}")


@router.get("/callback")
async def callback(
    msg_signature: str,
    timestamp: str,
    nonce: str,
    echostr: str,
    crypto: WXBizMsgCrypt = Depends(get_crypto)
):
    """接收企业微信回调验证请求
    
    Args:
        msg_signature: 企业微信加密签名
        timestamp: 时间戳
        nonce: 随机数
        echostr: 加密的随机字符串
        crypto: 加密工具实例
    
    Returns:
        Response: 解密后的明文消息，不带引号和BOM头
    """
    try:
        # URL解码
        msg_signature = unquote(msg_signature)
        timestamp = unquote(timestamp)
        nonce = unquote(nonce)
        echostr = unquote(echostr)
        
        # 验证签名并解密
        decrypted_str = crypto.verify_url(msg_signature, timestamp, nonce, echostr)
        
        # 返回纯文本响应，不带引号和BOM头
        return Response(
            content=decrypted_str,
            media_type="text/plain",
            headers={"Content-Type": "text/plain; charset=utf-8"}
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Verification failed: {str(e)}")

    
@router.get("/wx_config")
async def get_wx_config(url: str):
    """获取企业微信配置"""
    try:
        print('url: ', url)
        config = wechat_service.get_wx_config()
        # 基于URL和配置信息生成签名
        config["signature"] = wechat_service.get_signature(config["timestamp"], config["nonceStr"], url)

        # 返回json格式config
        print('config: ', config)
        return config
    except Exception as e:
        logger.error(f"Failed to get wx config: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get wx config: {str(e)}")