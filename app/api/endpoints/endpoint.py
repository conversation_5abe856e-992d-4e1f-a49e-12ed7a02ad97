
import json
import httpx
from urllib import parse
from loguru import logger
from typing import Optional
from app.core.config import settings
from fastapi import APIRouter, HTTPException
from fastapi.responses import RedirectResponse


router = APIRouter()

# 企业微信配置
CORP_ID = settings.CORP_ID
AGENT_ID = settings.AGENT_ID
APP_SECRET = settings.CORP_SECRET
FRONTEND_URL = settings.FRONTEND_URL  # 前端应用URL

class WechatWorkAPI:
    @staticmethod
    async def get_access_token():
        logger.info(f"Requesting access token for CORP_ID: {CORP_ID}")
        logger.info(f"Requesting access token for APP_SECRET: {APP_SECRET}")
        url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={CORP_ID}&corpsecret={APP_SECRET}"
        try:
            async with httpx.AsyncClient() as client:
                logger.info(f"Requesting access token for url: {url}")
                response = await client.get(url)
                data = response.json()
                
                logger.info(f"Access token response: {data}")
                
                if data.get("errcode") != 0:
                    error_msg = f"Failed to get access token: {data.get('errmsg', 'Unknown error')}"
                    logger.error(error_msg)
                    raise HTTPException(status_code=500, detail=error_msg)
                
                return data["access_token"]
        except httpx.RequestError as e:
            error_msg = f"Network error while getting access token: {str(e)}"
            logger.error(error_msg)
            raise HTTPException(status_code=500, detail=error_msg)
        except Exception as e:
            error_msg = f"Unexpected error while getting access token: {str(e)}"
            logger.error(error_msg)
            raise HTTPException(status_code=500, detail=error_msg)

    @staticmethod
    async def get_user_info(access_token: str, code: str):
        url = f"https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={access_token}&code={code}"
        async with httpx.AsyncClient() as client:
            response = await client.get(url)
            data = response.json()
            if data.get("errcode") != 0:
                raise HTTPException(status_code=500, detail="Failed to get user info")
            return data

    @staticmethod
    async def get_user_detail(access_token: str, userid: str):
        url = f"https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token={access_token}&userid={userid}"
        async with httpx.AsyncClient() as client:
            response = await client.get(url)
            data = response.json()
            if data.get("errcode") != 0:
                raise HTTPException(status_code=500, detail="Failed to get user detail")
            return data

@router.get("/api/wechat-work/auth")
async def wechat_work_auth(code: Optional[str] = None, state: Optional[str] = None):
    """
    处理企业微信认证回调
    接收企业微信重定向，获取用户信息，并重定向回前端应用
    """
    if not code:
        raise HTTPException(status_code=400, detail="Missing code parameter")

    if not CORP_ID or not APP_SECRET:
        error_msg = "Missing required environment variables"
        logger.error(error_msg)
        return RedirectResponse(f"{FRONTEND_URL}?error={error_msg}")

    try:
        # 1. 获取访问令牌
        access_token = await WechatWorkAPI.get_access_token()

        # 2. 使用code获取用户基本信息
        user_info = await WechatWorkAPI.get_user_info(access_token, code)
        userid = user_info.get("UserId")
        
        if not userid:
            raise HTTPException(status_code=401, detail="Failed to get user ID")

        # 3. 获取用户详细信息
        user_detail = await WechatWorkAPI.get_user_detail(access_token, userid)
        logger.info(f"User detail: {user_detail}")

        # 4. 格式化用户数据
        user_data = {
            "userId": user_detail.get("userid"),
            "name": user_detail.get("name"),
            "status": user_detail.get("status"),
            "telephone": user_detail.get("telephone"),
        }

        # 5. 将用户信息编码为URL参数
        encoded_user_data = parse.quote(json.dumps(user_data))
        redirect_url = f"{FRONTEND_URL}?userInfo={encoded_user_data}"
        
        logger.info(f"Redirecting to frontend with user data：{redirect_url}")
        return RedirectResponse(redirect_url)

    except HTTPException as e:
        error_msg = f"{e.status_code}: {e.detail}"
        logger.error(f"HTTP Exception: {error_msg}")
        return RedirectResponse(f"{FRONTEND_URL}?error={error_msg}")
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(f"Unexpected error: {error_msg}")
        return RedirectResponse(f"{FRONTEND_URL}?error={error_msg}")