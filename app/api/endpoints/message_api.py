import time
from pydantic import BaseModel
from typing import List, Union, Dict
from urllib.parse import unquote

from loguru import logger
import xml.etree.ElementTree as ET
from sqlalchemy.exc import IntegrityError
from fastapi import APIRouter, HTTPException, Depends, Request, Response

from app.core.config import settings
from app.models.db import DatabaseConnection
from app.models.push_record import PushRecord
from app.utils.wechat_crypto import WXBizMsgCrypt
from app.services.wechat_service import WeChatService
from app.services.mengchat_service import MengChatService
from app.models.message_processed import MessageProcessed
from app.handlers.message_handler import get_message_handler


router = APIRouter()
db = DatabaseConnection()
wechat_service = WeChatService()
mengchat_service = MengChatService()

class PushMessage(BaseModel):
    """推送消息模型"""
    user_ids: str
    party_ids: str
    content: Union[str, List[dict], Dict[str, str]]
    msg_type: str = "text"

def get_crypto() -> WXBizMsgCrypt:
    """获取企业微信加密类实例"""
    try:
        return WXBizMsgCrypt(
            token=settings.TOKEN,
            encoding_aes_key=settings.ENCODING_AES_KEY,
            corp_id=settings.CORP_ID
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to initialize WXBizMsgCrypt: {str(e)}")


@router.post("/callback")
async def callback_message(
    request: Request,
    msg_signature: str,
    timestamp: str,
    nonce: str,
    crypto: WXBizMsgCrypt = Depends(get_crypto)
):
    """接收企业微信消息推送
    
    Args:
        request: 请求对象
        msg_signature: 企业微信加密签名
        timestamp: 时间戳
        nonce: 随机数
        crypto: 加密工具实例
        
    Returns:
        Response: 处理结果
    """
    try:
        logger.debug("----------------------------------------------------------")
        start_time = time.time()
        logger.info("收到企业微信消息回调请求 - 签名: {}, 时间戳: {}, 随机数: {}", msg_signature, timestamp, nonce)
        
        # URL解码
        try:
            msg_signature = unquote(msg_signature)
            timestamp = unquote(timestamp)
            nonce = unquote(nonce)
            logger.debug("URL解码完成")
        except Exception as e:
            logger.error("URL解码失败: {}", str(e))
            return Response("success")  # 返回成功避免重试
        
        # 读取请求体
        msg_id = ""
        decrypted_msg = ""
        try:
            body = await request.body()
            xml_content = body.decode()
            logger.debug("收到原始XML内容: {}", xml_content)

            # 解析XML获取加密消息
            root = ET.fromstring(xml_content)
            encrypt_msg = root.find("Encrypt").text
            logger.debug("成功解析XML获取加密消息")
            
            # 验证签名并解密消息
            decrypted_msg = crypto.decrypt(encrypt_msg)
            logger.info("消息解密成功，消息内容：{}", decrypted_msg)
            
            # 解析XML获取消息ID
            root = ET.fromstring(decrypted_msg)
            msg_id = root.find("MsgId")
            if msg_id is not None:
                msg_id = msg_id.text
            else:
                # 如果没有MsgId，使用签名作为ID
                msg_id = msg_signature
        except Exception as e:
            logger.error("解析消息失败: {}", str(e))
            return Response("success")  # 返回成功避免重试

        # 解析消息
        message = wechat_service.parse_message(decrypted_msg)
        logger.info("收到消息类型: {}, 来自用户: {}", message.MsgType, message.FromUserName)

        # 判断消息类型
        if message.MsgType == "event" and message.Event == "view":
            # 事件消息
            logger.info("收到页面浏览事件消息，不处理。消息类型: {}, 事件: {}", message.MsgType, message.Event)
            return Response("success")

        try:
            # 标记处理消息
            with db.session_marker() as session:
                processed = MessageProcessed(
                    msg_id=msg_id
                )
                session.add(processed)
                session.commit()
                logger.info("消息未处理过，开始处理: {}", msg_id)
        except IntegrityError as e:
            logger.error("消息已处理过，跳过处理: {}", msg_id)
            return Response("success")
        
        try:
            # 发送等待生成提醒消息
            wechat_service.send_message(message.FromUserName, "请稍等，MengChat正在全力思考中...", msg_type="text")

            # 获取对应的消息处理器并处理消息
            handler = get_message_handler(message.MsgType)
            logger.debug("获取消息处理器成功，开始处理消息: {}", message.MsgType)
            await handler.handle(message, start_time)

            # 发送引导使用新版本消息
            msg_content = "MengChat办公助手近期已进行全面升级，请在聊天框底部切换输入方式为菜单模式，选择并点击开始聊天按钮进入新版本，体验更流畅的对话！"
            wechat_service.send_message(message.FromUserName, msg_content, msg_type="text")
            return Response("success")
        except Exception as e:
            error_msg = f"处理消息失败: {str(e)}"
            logger.error(error_msg)
            wechat_service.send_message(message.FromUserName, "系统繁忙，请稍后重试", msg_type="text")
            raise HTTPException(status_code=500, detail=error_msg)
    except ValueError as e:
        error_msg = f"无效的请求参数: {str(e)}"
        logger.error(error_msg)
        wechat_service.send_message(message.FromUserName, "系统繁忙，请稍后重试", msg_type="text")
        raise HTTPException(status_code=400, detail=error_msg)


@router.post("/push")
async def push_message(message: PushMessage):
    """主动推送消息到企业微信

    Args:
        message: 推送消息模型，包含接收者ID列表和消息内容

    Returns:
        dict: 推送结果
    """
    logger.info(f"开始推送消息: {message}")
    start = time.time()
    push_record = None
    
    try:
        # 第一步：创建推送记录
        with db.session_marker() as session:
            push_record = PushRecord(
                user_ids=message.user_ids,
                party_ids=message.party_ids,
                content=str(message.content),
                msg_type=message.msg_type,
                status="pending",
                environment=settings.ENVIRONMENT
            )
            session.add(push_record)
            session.commit()
            record_id = push_record.id  # 获取记录ID
        
        # 第二步：调用企业微信API发送消息
        result = wechat_service.send_message(
            user_id=message.user_ids,
            party_id=message.party_ids,
            content=message.content,
            msg_type=message.msg_type
        )
        
        # 第三步：更新推送记录状态
        with db.session_marker() as session:
            push_record = session.query(PushRecord).get(record_id)
            if push_record:
                process_time = int((time.time() - start) * 1000)
                push_record.process_time = process_time
                push_record.status = "success"
                session.commit()

        return result
                
    except Exception as e:
        # 更新推送记录状态为失败
        if push_record and push_record.id:
            try:
                with db.session_marker() as session:
                    push_record = session.query(PushRecord).get(push_record.id)
                    if push_record:
                        process_time = int((time.time() - start) * 1000)
                        push_record.process_time = process_time
                        push_record.status = "failed"
                        push_record.error = str(e)
                        session.commit()
            except Exception as db_error:
                logger.error("更新推送记录失败: {}", str(db_error))
        
        error_msg = f"推送消息失败: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)
