from loguru import logger
from datetime import datetime
from pydantic import BaseModel
from typing import Optional, List
from fastapi import APIRouter, HTTPException, Depends, Header

from app.services.tool_service import record_tool_click, get_user_tool_clicks, get_tool_click_stats
from app.api.chat.chat_message_api import verify_api_key


router = APIRouter()

# 数据模型
class ToolClickRequest(BaseModel):
    user_id: str
    tool_name: str
    tool_type: str
    click_time: Optional[datetime] = None

class ToolClickResponse(BaseModel):
    id: int
    user_id: str
    tool_name: str
    tool_type: str
    click_time: datetime
    created_at: datetime

class ToolClickStatsResponse(BaseModel):
    tool_name: str
    tool_type: str
    click_count: int


@router.post("/api/tool/click")
async def tool_click(request: ToolClickRequest, token: bool = Depends(verify_api_key)):
    """记录用户工具点击信息"""
    try:
        # 参数校验
        if not request.user_id or not request.tool_name or not request.tool_type:
            logger.error("记录工具点击失败: 参数不完整")
            raise HTTPException(status_code=400, detail="参数不完整")

        # 调用服务记录工具点击
        record_id = record_tool_click(
            request.user_id,
            request.tool_name,
            request.tool_type,
            datetime.now()
        )

        logger.info(f"已记录工具点击: 用户 {request.user_id} 点击了 {request.tool_name}")
        return {"success": True, "message": "记录工具点击成功", "data": {"id": record_id}}
    except Exception as e:
        logger.error(f"记录工具点击时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"记录工具点击失败: {str(e)}")


@router.get("/api/tool/user/{user_id}")
async def get_user_tool_click_records(user_id: str, token: bool = Depends(verify_api_key)):
    """获取用户的工具点击记录"""
    try:
        if not user_id:
            logger.error("获取用户工具点击记录失败: 用户ID不能为空")
            raise HTTPException(status_code=400, detail="用户ID不能为空")

        records = get_user_tool_clicks(user_id)
        logger.info(f"已获取用户 {user_id} 的工具点击记录，共 {len(records)} 条")
        return {"success": True, "message": "获取用户工具点击记录成功", "data": records}
    except Exception as e:
        logger.error(f"获取用户工具点击记录时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取用户工具点击记录失败: {str(e)}")


@router.get("/api/tool/stats")
async def get_tool_click_statistics(tool_type: Optional[str] = None, token: bool = Depends(verify_api_key)):
    """获取工具点击统计数据"""
    try:
        stats = get_tool_click_stats(tool_type)
        logger.info(f"已获取工具点击统计数据，共 {len(stats)} 条")
        return {"success": True, "message": "获取工具点击统计数据成功", "data": stats}
    except Exception as e:
        logger.error(f"获取工具点击统计数据时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取工具点击统计数据失败: {str(e)}")
