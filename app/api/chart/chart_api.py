import os
import time
import json
from loguru import logger
from typing import Optional, Dict, Any
from pydantic import BaseModel
from fastapi import APIRouter, HTTPException, Depends, Header
from openai import OpenAI
from app.core.config import settings
from json_repair import repair_json
from app.services.chart_llm_service import record_chart_llm_request


# 创建路由器
router = APIRouter()

# 定义请求模型
class ChartLLMRequest(BaseModel):
    query: str
    prompt: str
    user_id: Optional[str] = None
    model_type: Optional[str] = "local"
    temperature: Optional[float] = 0.2
    max_tokens: Optional[int] = 8192

# 定义响应模型
class ChartLLMResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

# 验证API密钥
def verify_api_key(authorization: str = Header(...)):
    api_key = os.environ.get("API_KEY", "c2stbWVuZ2NoYXQtYm90IQ==")
    if authorization != f"Bearer {api_key}":
        raise HTTPException(status_code=401, detail="无效的API密钥")
    return True

@router.post("/api/chart/llm", response_model=ChartLLMResponse)
async def generate_chart_with_llm(
    request: ChartLLMRequest,
    api_key_valid: bool = Depends(verify_api_key)
):
    """
    使用LLM生成图表数据接口

    接收前端传来的prompt参数，调用deepseek API生成图表数据

    Args:
        request: 包含prompt和可选参数的请求对象

    Returns:
        ChartLLMResponse: 包含处理结果的响应对象
    """
    start_time = time.time()
    record_id = None

    # 准备模型参数
    model_params = {
        "model": settings.LOCAL_MODEL_API_MODEL if request.model_type == "local" else settings.DEEPSEEK_API_MODEL,
        "temperature": request.temperature,
        "max_tokens": request.max_tokens,
        "model_type": request.model_type
    }

    try:
        logger.info(f"========== 接收到图表生成请求，用户: {request.user_id} \n 模型：{request.model_type} \n 需求: {request.query}... \n 参数: {model_params}")

        # ================ 根据model_type选择调用方式 ================
        if request.model_type == "local":
            # 创建客户端
            client = OpenAI(base_url=settings.LOCAL_MODEL_API_URL, api_key=settings.LOCAL_MODEL_API_KEY)
            # 调用本地模型
            response = client.chat.completions.create(
                model=settings.LOCAL_MODEL_API_MODEL,
                stream=False,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                messages=[
                    {"role": "user", "content": request.prompt}
                ]
            )
            result = response.choices[0].message.content.strip()
        else:
             # 创建客户端
            client = OpenAI(base_url=settings.DEEPSEEK_API_URL, api_key=settings.DEEPSEEK_API_KEY)
            # 调用deepseek API
            response = client.chat.completions.create(
                model=settings.DEEPSEEK_API_MODEL,
                stream=False,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                messages=[
                    {"role": "user", "content": request.prompt}
                ]
            )
            result = response.choices[0].message.content.strip()

        # 提取生成结果
        process_time_seconds = round(time.time() - start_time, 2)
        process_time_ms = int(process_time_seconds * 1000)
        
        # 结果格式化
        if "</think>" in result:
            result = result.split("</think>")[1]
        # result = result.replace("```json", "").replace("```", "")
        # result = repair_json(result)
        logger.info(f"图表数据生成完成，耗时: {process_time_seconds}秒，结果: {result}")

        # # 解码JSON中的Unicode中文字符
        # try:
        #     # 如果result是字符串形式的JSON，先解析成Python对象
        #     if isinstance(result, str):
        #         result_obj = json.loads(result)
        #         # 将Python对象重新编码为JSON字符串，确保中文正常显示
        #         result = json.dumps(result_obj, ensure_ascii=False)
        # except Exception as decode_error:
        #     logger.warning(f"解码中文字符失败: {str(decode_error)}")

        # 记录成功的请求
        try:
            record_id = record_chart_llm_request(
                user_id=request.user_id,
                query=request.query,
                prompt=request.prompt,
                model_params=model_params,
                result_content=result,
                process_time=process_time_ms,
                status="success"
            )
            logger.info(f"成功记录图表LLM请求，记录ID: {record_id}")
        except Exception as record_error:
            logger.error(f"记录图表LLM请求失败: {str(record_error)}")

        # 返回结果
        return ChartLLMResponse(
            success=True,
            message="图表数据生成成功",
            data={
                "result": result,
                "process_time": process_time_seconds,
                "record_id": record_id
            }
        )
    except Exception as e:
        logger.error(f"生成图表数据时出错: {str(e)}")
        process_time_seconds = round(time.time() - start_time, 2)
        process_time_ms = int(process_time_seconds * 1000)

        # 记录失败的请求
        try:
            record_id = record_chart_llm_request(
                user_id=request.user_id,
                query=request.query,
                prompt=request.prompt,
                model_params=model_params,
                result_content=None,
                process_time=process_time_ms,
                status="failed",
                error_message=str(e)
            )
            logger.info(f"成功记录失败的图表LLM请求，记录ID: {record_id}")
        except Exception as record_error:
            logger.error(f"记录失败的图表LLM请求失败: {str(record_error)}")

        return ChartLLMResponse(
            success=False,
            message=f"生成图表数据失败: {str(e)}",
            data={
                "result": None,
                "process_time": process_time_seconds,
                "record_id": record_id
            }
        )