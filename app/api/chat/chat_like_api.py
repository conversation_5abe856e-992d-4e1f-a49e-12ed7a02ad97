import os
import time
from loguru import logger
from typing import Optional
from datetime import datetime
from pydantic import BaseModel
from fastapi import APIRouter, HTTPException, Depends, Header
from app.services.feedback_service import record_feedback_message


router = APIRouter()

# 定义请求模型
class FeedbackModel(BaseModel):
    userId: str
    userQuestion: str
    assistantAnswer: str
    timestamp: Optional[str] = None

# 验证API密钥
def verify_api_key(authorization: str = Header(...)):
    api_key = os.environ.get("API_KEY", "c2stbWVuZ2NoYXQtYm90IQ==")
    if authorization != f"Bearer {api_key}":
        raise HTTPException(status_code=401, detail="无效的API密钥")
    return True

# 保存反馈到文件
def save_feedback(feedback: dict, feedback_type: str):
    try:
        # 确保每个反馈都有唯一标识符
        feedback_id = f"{feedback_type}_{int(time.time() * 1000)}_{feedback['userId']}"
        feedback["id"] = feedback_id
        logger.info(f"保存{feedback_type}反馈: {feedback_id}, 数据: {feedback}")

        # 调用服务保存反馈
        record_feedback_message(feedback['userId'], feedback['userQuestion'], feedback['assistantAnswer'], feedback_type)
        logger.info(f"保存{feedback_type}反馈: {feedback_id}")
        return feedback_id
    except Exception as e:
        logger.error(f"保存反馈失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存反馈失败: {str(e)}")

@router.post("/api/feedback/like")
async def like_answer(feedback: FeedbackModel, api_key_valid: bool = Depends(verify_api_key)):
    """
    点赞回答接口
    """
    try:
        if not feedback.timestamp:
            feedback.timestamp = datetime.now().isoformat()
        
        # 转换为字典并添加反馈类型
        feedback_dict = feedback.model_dump()
        feedback_dict["feedback_type"] = "like"
        
        # 保存反馈
        feedback_id = save_feedback(feedback_dict, "like")
        
        return {
            "success": True,
            "message": "点赞成功",
            "feedback_id": feedback_id
        }
    except Exception as e:
        logger.error(f"点赞处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"点赞处理失败: {str(e)}")

@router.post("/api/feedback/dislike")
async def dislike_answer(feedback: FeedbackModel, api_key_valid: bool = Depends(verify_api_key)):
    """
    点踩回答接口
    """
    try:
        if not feedback.timestamp:
            feedback.timestamp = datetime.now().isoformat()
        
        # 转换为字典并添加反馈类型
        feedback_dict = feedback.model_dump()
        feedback_dict["feedback_type"] = "dislike"
        
        # 保存反馈
        feedback_id = save_feedback(feedback_dict, "dislike")
        
        return {
            "success": True,
            "message": "点踩成功",
            "feedback_id": feedback_id
        }
    except Exception as e:
        logger.error(f"点踩处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"点踩处理失败: {str(e)}")