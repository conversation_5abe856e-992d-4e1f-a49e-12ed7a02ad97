import json
import os
import base64
import aiohttp
import requests
import asyncio
from loguru import logger
from datetime import datetime
from pydantic import BaseModel
from typing import Optional, List
from json_repair import repair_json
from fastapi.responses import StreamingResponse, Response
from fastapi import APIRouter, HTTPException, Depends, Header, Query
from app.services.message_service import (
    record_message, 
    get_user_conversations, 
    create_conversation, 
    update_conversation, 
    delete_conversation, 
    get_conversation_messages, 
    add_message_to_conversation
)
from app.core.config import settings
from app.utils.connection_manager import connection_manager


router = APIRouter()

# 数据模型
class ConversationLog(BaseModel):
    user_id: str
    message_content: str
    response_content: str
    message_type: str
    status: int
    error_message: Optional[str] = None
    process_time: int
    session_id: Optional[str] = None
    session_name: Optional[str] = None
    attachment_url: Optional[str] = None

class ConversationCreate(BaseModel):
    user_id: str
    title: str
    session_id: str

class ConversationUpdate(BaseModel):
    user_id: str
    title: Optional[str] = None

class MessageCreate(BaseModel):
    user_id: str
    role: str  # user/assistant
    content: str
    thinking: Optional[str] = None
    process_time: Optional[int] = None
    attachments: Optional[List[dict]] = None
    timestamp: Optional[int] = None

class TTSRequest(BaseModel):
    text: str
    voice: Optional[str] = "default"
    speed: Optional[float] = 1.0
    pitch: Optional[float] = 1.0

class SessionResponse(BaseModel):
    session_id: str
    user_id: str
    session_name: Optional[str] = None
    last_message_time: datetime
    message_count: int

class MessageResponse(BaseModel):
    id: int
    user_id: str
    session_id: str
    session_name: Optional[str] = None
    message_content: str
    response_content: str
    message_type: str
    status: int
    process_time: Optional[int] = None
    created_at: datetime
    updated_at: datetime

# 请求参数模型（兼容OpenAI协议）
class ChatCompletionRequest(BaseModel):
    chatId: str
    messages: List[dict]
    stream: Optional[bool] = False
    detail: Optional[bool] = False
    variables: Optional[dict] = None

# 验证API密钥
def verify_api_key(authorization: str = Header(...)):
    api_key = os.environ.get("API_KEY", "c2stbWVuZ2NoYXQtYm90IQ==")
    if authorization != f"Bearer {api_key}":
        raise HTTPException(status_code=401, detail="无效的API密钥")
    return True


# ========== 会话管理 API ==========

@router.get("/api/conversations")
async def get_conversations(
    user_id: str = Query(..., description="用户ID"),
    limit: int = Query(50, description="返回数量限制"),
    token: bool = Depends(verify_api_key)
):
    """获取用户的所有会话列表"""
    try:
        conversations = get_user_conversations(user_id, limit)
        return {
            "success": True,
            "data": conversations
        }
    except Exception as e:
        logger.error(f"获取会话列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")


@router.post("/api/conversations")
async def create_conversation_api(
    conversation: ConversationCreate,
    token: bool = Depends(verify_api_key)
):
    """创建新会话"""
    try:
        created_conversation = create_conversation(
            conversation.user_id,
            conversation.title,
            conversation.session_id
        )
        return {
            "success": True,
            "data": created_conversation
        }
    except Exception as e:
        logger.error(f"创建会话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建会话失败: {str(e)}")


@router.get("/api/conversations/{conversation_id}")
async def get_conversation_detail(
    conversation_id: str,
    user_id: str = Query(..., description="用户ID"),
    token: bool = Depends(verify_api_key)
):
    """获取特定会话的详细信息"""
    try:
        # 获取会话的所有消息
        messages = get_conversation_messages(user_id, conversation_id)
        
        # 构建会话信息（从消息中推断）
        if messages:
            conversation = {
                "id": conversation_id,
                "sessionId": conversation_id,
                "messages": messages
            }
            return {
                "success": True,
                "data": conversation
            }
        else:
            raise HTTPException(status_code=404, detail="会话不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话详情失败: {str(e)}")


@router.put("/api/conversations/{conversation_id}")
async def update_conversation_api(
    conversation_id: str,
    conversation: ConversationUpdate,
    token: bool = Depends(verify_api_key)
):
    """更新会话信息（重命名）"""
    try:
        updated_conversation = update_conversation(
            conversation.user_id,
            conversation_id,
            conversation.title
        )
        return {
            "success": True,
            "data": updated_conversation
        }
    except Exception as e:
        logger.error(f"更新会话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新会话失败: {str(e)}")


@router.delete("/api/conversations/{conversation_id}")
async def delete_conversation_api(
    conversation_id: str,
    user_id: str = Query(..., description="用户ID"),
    token: bool = Depends(verify_api_key)
):
    """删除会话"""
    try:
        success = delete_conversation(user_id, conversation_id)
        if success:
            return {
                "success": True,
                "message": "会话删除成功"
            }
        else:
            raise HTTPException(status_code=404, detail="会话不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除会话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除会话失败: {str(e)}")


# ========== 消息管理 API ==========

@router.get("/api/conversations/{conversation_id}/messages")
async def get_conversation_messages_api(
    conversation_id: str,
    user_id: str = Query(..., description="用户ID"),
    limit: int = Query(50, description="返回数量限制"),
    offset: int = Query(0, description="偏移量"),
    token: bool = Depends(verify_api_key)
):
    """获取特定会话的消息历史"""
    try:
        messages = get_conversation_messages(user_id, conversation_id, limit, offset)
        return {
            "success": True,
            "data": messages
        }
    except Exception as e:
        logger.error(f"获取会话消息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话消息失败: {str(e)}")


@router.post("/api/conversations/{conversation_id}/messages")
async def add_message_to_conversation_api(
    conversation_id: str,
    message: MessageCreate,
    token: bool = Depends(verify_api_key)
):
    """添加消息到会话"""
    try:
        created_message = add_message_to_conversation(
            message.user_id,
            conversation_id,
            message.role,
            message.content,
            message.thinking,
            message.process_time,
            message.attachments,
            message.timestamp
        )
        return {
            "success": True,
            "data": created_message
        }
    except Exception as e:
        logger.error(f"添加消息到会话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加消息到会话失败: {str(e)}")


# ========== 原有接口保持兼容 ==========

@router.post("/api/v1/chat/abort")
async def abort_chat_completion(
    chat_id: str = Query(..., description="聊天会话ID"),
    token: bool = Depends(verify_api_key)
):
    """
    中断聊天会话的AI回复
    
    Args:
        chat_id: 聊天会话ID
        
    Returns:
        中断结果
    """
    try:
        logger.info(f"收到中断请求，chatId: {chat_id}")
        
        # 使用连接管理器中断连接
        success = await connection_manager.abort_connection(chat_id)
        
        if success:
            logger.info(f"成功中断聊天会话: {chat_id}")
            return {
                "success": True,
                "message": f"聊天会话 {chat_id} 已中断",
                "chat_id": chat_id
            }
        else:
            logger.warning(f"没有找到活跃的连接: {chat_id}")
            return {
                "success": False,
                "message": f"聊天会话 {chat_id} 没有活跃的连接",
                "chat_id": chat_id
            }
            
    except Exception as e:
        logger.error(f"中断聊天会话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"中断聊天会话失败: {str(e)}")


@router.post("/api/v1/chat/completions")
async def chat_completion(request: ChatCompletionRequest, token: bool = Depends(verify_api_key)):
    connection_id = None
    try:
        logger.info(f"开始执行AI接口调用，请求参数：\n- chatId: {request.chatId}\n- userId: {request.variables.get('userId')}\n- modelType: {request.variables.get('modelTypeCalled')}\n- messages: {request.messages[0].get('content')[0].get('text')}")
        url = settings.MENGCHAT_API_URL
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {settings.MENGCHAT_API_KEY}"
        }
        # 前置校验：流程权限检查
        check_result, data = await flow_permission_check(request)
        if not check_result:
            return StreamingResponse(content=data, media_type="text/event-stream")

        # 前置处理：图片URL解码
        await image_url_decode(request)

        # 前置处理：补充默认图片
        await add_default_image(request)

        data = request.model_dump()
        
        async def generate():
            connection_id = None
            session = None
            response = None
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, headers=headers, json=data, timeout=180.0) as response:
                        # 创建连接并注册到管理器
                        connection_id = await connection_manager.create_connection(
                            request.chatId, session, response
                        )
                        logger.info(f"已注册连接到管理器: {connection_id}")
                        
                        is_break = False
                        is_nodes = False
                        
                        try:
                            async for chunk in response.content.iter_chunks():
                                if chunk:
                                    try:
                                        chunk_data = chunk[0].decode('utf-8')
                                    except Exception as e:
                                        # 如果解码失败，尝试其他编码或跳过此chunk
                                        try:
                                            chunk_data = chunk[0].decode('gbk')
                                        except:
                                            logger.error(f"Failed to decode chunk: {e}")
                                            continue
                                    # 已完成，跳过
                                    if is_break: 
                                        break

                                    # 节点，解析
                                    if is_nodes:
                                        json_data = chunk_data[5:].strip()
                                        try:
                                            json_data = json.loads(json_data)
                                        except json.JSONDecodeError:
                                            # 使用repair_json修复无效的JSON
                                            json_data = repair_json(json_data)
                                            json_data = json.loads(json_data)
                                
                                    if '[DONE]' in chunk_data:
                                        is_break = True

                                    if 'event' in chunk_data and 'flowNodeStatus' in chunk_data:
                                        is_nodes = True
                                    else:
                                        is_nodes = False

                                    if 'data' in chunk_data and '[DONE]' not in chunk_data:
                                        json_data = chunk_data[5:].strip()
                                        try:
                                            json_data = json.loads(json_data)
                                            yield 'data: ' + json.dumps(json_data) + "\n\n"
                                        except json.JSONDecodeError:
                                            # 使用repair_json修复无效的JSON
                                            try:
                                                repaired_json = repair_json(json_data)
                                                json_data = json.loads(repaired_json)
                                                yield 'data: ' + json.dumps(json_data) + "\n\n"
                                            except Exception as repair_error:
                                                logger.warning(f"JSON repair failed: {repair_error}, original data: {json_data}")
                                                yield 'data: ' + json_data + "\n\n"
                                        except Exception as e:
                                            logger.error(f"Unexpected error parsing JSON: {e}")
                                            yield 'data: ' + json_data + "\n\n"
                                    else:
                                        yield chunk[0].decode('utf-8') + "\n\n"
                        except asyncio.CancelledError:
                            logger.info(f"连接被取消: {connection_id}")
                            raise
                        except aiohttp.ClientConnectionError as e:
                            logger.warning(f"连接被中断: {connection_id}, 错误: {str(e)}")
                            # 连接中断不抛出异常，正常结束
                            return
                        except Exception as e:
                            logger.error(f"流式处理错误: {str(e)}")
                            raise
                            
            except asyncio.CancelledError:
                logger.info(f"任务被取消: {connection_id}")
                # 任务被取消，正常结束
                return
            except aiohttp.ClientConnectionError as e:
                logger.warning(f"客户端连接错误: {str(e)}")
                # 连接错误，可能是用户中断
                return
            except Exception as e:
                logger.error(f"生成器内部错误: {str(e)}")
                yield f'data: {{"error": "连接已中断"}}\n\n'
            finally:
                # 清理连接
                if connection_id:
                    try:
                        await connection_manager.cleanup_connection(connection_id)
                        logger.info(f"已清理连接: {connection_id}")
                    except Exception as e:
                        logger.error(f"清理连接失败: {str(e)}")
        
        return StreamingResponse(generate(), media_type="text/event-stream")
    
    except Exception as e:
        logger.error(f"FastGPT MengChat API 调用错误: {e}")
        # 确保在异常情况下也清理连接
        if connection_id:
            try:
                await connection_manager.cleanup_connection(connection_id)
            except Exception as cleanup_error:
                logger.error(f"异常清理连接失败: {str(cleanup_error)}")
        return HTTPException(status_code=500, detail=f"服务器繁忙，请稍后重试")

# 路由
@router.post("/api/chat_message/log")
async def conversation_log(log: ConversationLog, token: bool = Depends(verify_api_key)):
    """保存对话日志"""
    try:
        # 参数校验
        log_dict = log.model_dump()
        if not log_dict.get("user_id"):
            logger.error("保存对话日志失败: 缺少user_id参数")
            raise HTTPException(status_code=400, detail="缺少user_id参数")
        
        # 如果response_content为空，设置为空字符串
        if not log_dict.get("response_content"):
            log_dict["response_content"] = ""

        # 调用服务保存对话日志
        session_id = save_conversation_log(log_dict)

        logger.info(f"已保存对话日志: {log_dict['user_id']}, 会话ID: {session_id}")
        log_dict['session_id'] = session_id
        return {"success": True, "message": "保存对话日志成功", "data": log_dict}
    except Exception as e:
        logger.error(f"保存对话日志时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存对话日志失败: {str(e)}")

# 保存对话日志
def save_conversation_log(log: dict):
    try:
        # 调用服务保存对话日志
        session_id = record_message(
            log['user_id'],
            log['message_content'],
            log['response_content'],
            log['message_type'],
            log['process_time'],
            log['status'],
            log.get('error_message'),
            log.get('session_id'),
            log.get('session_name'),
            log.get('attachment_url')
        )
        logger.info(f"保存用户对话日志成功, 会话ID: {session_id}")
        return session_id
    except Exception as e:
        logger.error(f"保存对话日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存对话日志失败: {str(e)}")

async def flow_permission_check(request: ChatCompletionRequest):
    """
    流程权限检查
    
    Returns:
        bool: 是否通过权限检查
        str: 检查结果消息
    """
    try:
        chatId = request.chatId
        msg_type = request.messages[0].get("content")[0].get("type")
        if msg_type == "text":
            content = request.messages[0].get("content")[0].get("text")
            # 待办/已办越权检查
            if "/待办流程" in content or "/已办流程" in content:
                userId = request.variables.get("userId")
                # 判断用户ID是否匹配消息ID
                if userId not in chatId:
                    msg = "流程信息查询失败，您只能访问您自己的流程信息"
                    data = 'data: {"id":"","object":"","created":0,"choices":[{"delta":{"content":"'+msg+'"},"index":0,"finish_reason":null}]}'
                    data += '\ndata: [DONE]'
                    logger.warning(f"流程信息查询失败，前置校验失败，只能访问自己的流程")
                    return False, data

        return True, ""
    except Exception as e:
        logger.error(f"参数校验失败: {str(e)}")
        return False, str(e)

async def image_url_decode(request: ChatCompletionRequest):
    """
    图片URL解码
    """
    try:
        for msg in request.messages:
            for content in msg.get("content", []):
                if content.get("type") == "image_url":
                    image_url = content.get("image_url").get("url")
                    # base64解码图片URL
                    decoded_url = url_safe_b64decode(image_url)
                    content["image_url"]["url"] = decoded_url
                    logger.info(f"图片URL解码成功，image_id: {image_url}  解码后的URL: {decoded_url}")
    except Exception as e:
        logger.error(f"图片URL解码失败, image_id: {image_url}  错误信息：{str(e)}")

def url_safe_b64decode(data):
    """
    URL安全的Base64解码
    """
    # 替换URL安全字符为标准Base64字符
    data = data.replace('-', '+').replace('_', '/')
    # 添加必要的填充
    padding = len(data) % 4
    if padding:
        data += '=' * (4 - padding)
    return base64.b64decode(data).decode('utf-8')

async def add_default_image(request: ChatCompletionRequest):
    """
    补充默认图片：当消息中包含 /ocr 关键词时，判断消息中是否包含图片，如果不包含，则将默认图片补充到消息中
    """
    default_base64_url = "aHR0cHM6Ly9vbmxpbmVzYWxlcy1wcm9kLWZ0cC5ncm91cGFtYS1zZGlnLmNvbTo4MDIzL0RaWHBpYy9wcm9kdWN0UElDL2QzYjBjODkyYmE3NTQ1NmNiMDQwOTk4YjQ0MzBiNTdkLnBuZw=="
    try:
        is_iamge_msg = False
        # 判断是否包含 /ocr 关键词
        for msg in request.messages:
            for content in msg.get("content", []):
                if content.get("type") == "text":
                    text = content.get("text")
                    if "/ocr" in text:
                        is_iamge_msg = True
        
        if is_iamge_msg:
            # 判断是否包含图片类型消息，不包含时自动添加默认图片
            if any(content.get("type") == "image_url" for content in request.messages[0].get("content", [])):
                logger.info("消息中已包含图片类型消息，不补充默认图片")
            else:
                logger.info("消息中不包含图片类型消息，自动添加默认图片")
                request.messages[0].get("content").append({
                    "type": "image_url",
                    "image_url": {"url": url_safe_b64decode(default_base64_url)}
                })
    except Exception as e:
        logger.error(f"补充默认图片失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"补充默认图片失败: {str(e)}")


# ========== 辅助函数 ==========

def get_api_token():
    """
    获取API访问token
    """
    try:
        headers = {
            'Content-Type': 'application/json',
        }
        data = {
            'app_id': settings.LLM_SERVICE_APP_ID,
            'app_secret': settings.LLM_SERVICE_APP_SECRET
        }
        token_url = settings.LLM_SERVICE_TOKEN_URL
        
        logger.info(f"开始获取API token: {token_url}")
        response = requests.post(url=token_url, headers=headers, json=data, timeout=10)
        
        if response.status_code == 200:
            response_data = response.json()
            token = response_data.get('data')
            if token:
                logger.info("API token获取成功")
                return token
            else:
                logger.error("API token响应中没有data字段")
                return None
        else:
            logger.error(f"获取API token失败: {response.status_code}, {response.text}")
            return None
            
    except requests.RequestException as e:
        logger.error(f"获取API token网络错误: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"获取API token异常: {str(e)}")
        return None


# ========== TTS 文本转语音 API ==========

@router.post("/api/tts")
async def text_to_speech(
    request: TTSRequest,
    token: bool = Depends(verify_api_key)
):
    """
    文本转语音接口
    """
    try:
        logger.info(f"开始TTS请求，文本长度: {len(request.text)}")
        
        # 获取API访问token
        api_token = get_api_token()
        if not api_token:
            logger.error("获取API token失败")
            raise HTTPException(status_code=500, detail="获取API访问凭证失败")
        
        # 远程TTS服务配置
        tts_service_url = settings.TTS_API_URL
        
        # 准备请求数据
        tts_request_data = {
            "text": request.text
        }
        
        # 设置请求头，包含Authorization Bearer token
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_token}"
        }
        
        # 调用远程TTS服务
        async with aiohttp.ClientSession() as session:
            logger.info(f"调用远程TTS服务: {tts_service_url}")
            logger.debug(f"请求数据: {tts_request_data}")
            
            async with session.post(
                tts_service_url,
                headers=headers,
                json=tts_request_data,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"TTS服务返回错误: {response.status}, {error_text}")
                    raise HTTPException(
                        status_code=response.status,
                        detail=f"TTS服务错误: {error_text}"
                    )
                
                # 解析响应
                response_data = await response.json()
                logger.info(f"TTS服务响应: {response_data}")
                
                # 检查响应格式
                if response_data.get("code") != 200:
                    error_msg = response_data.get("message", "TTS服务返回错误")
                    logger.error(f"TTS服务业务错误: {error_msg}")
                    raise HTTPException(status_code=500, detail=error_msg)
                
                # 获取音频文件路径
                audio_file_url = response_data.get("data")
                if not audio_file_url:
                    logger.error("TTS服务返回的数据中没有音频文件路径")
                    raise HTTPException(status_code=500, detail="TTS服务返回数据格式错误")
                
                logger.info(f"获取音频文件URL: {audio_file_url}")
                
                # 下载音频文件
                async with session.get(audio_file_url) as audio_response:
                    if audio_response.status != 200:
                        logger.error(f"下载音频文件失败: {audio_response.status}")
                        raise HTTPException(status_code=500, detail="音频文件下载失败")
                    
                    # 读取音频数据
                    audio_data = await audio_response.read()
                    logger.info(f"音频文件下载完成，大小: {len(audio_data)} bytes")
                    
                    # 获取音频文件的MIME类型
                    content_type = audio_response.headers.get('content-type', 'audio/mpeg')
                    
                    # 返回音频文件流
                    return Response(
                        content=audio_data,
                        media_type=content_type,
                        headers={
                            "Content-Disposition": "attachment; filename=tts_audio.mp3",
                            "Access-Control-Allow-Origin": "*",
                            "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
                            "Access-Control-Allow-Headers": "Content-Type, Authorization"
                        }
                    )
                    
    except aiohttp.ClientTimeout:
        logger.error("TTS服务调用超时")
        raise HTTPException(status_code=504, detail="TTS服务响应超时，请稍后重试")
    
    except aiohttp.ClientError as e:
        logger.error(f"TTS服务连接错误: {str(e)}")
        raise HTTPException(status_code=502, detail="无法连接到TTS服务")
    
    except HTTPException:
        # 重新抛出已处理的HTTP异常
        raise
    
    except Exception as e:
        logger.error(f"TTS请求处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"TTS服务内部错误: {str(e)}")
