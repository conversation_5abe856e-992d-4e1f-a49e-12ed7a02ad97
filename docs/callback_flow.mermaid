sequenceDiagram
    participant WX as 企业微信服务器
    participant API as MengChat API
    participant Crypto as 加密工具
    participant Handler as 消息处理器
    participant <PERSON>gChat as MengChat服务
    participant DB as 数据库

    %% GET 请求流程 - 服务器验证
    Note over WX,API: GET /callback - 服务器验证流程
    WX->>API: GET /callback<br/>携带msg_signature,timestamp,nonce,echostr
    API->>Crypto: 调用verify_url验证签名
    Crypto-->>API: 返回解密后的echostr
    API-->>WX: 返回解密后的echostr

    %% POST 请求流程 - 消息处理
    Note over WX,API: POST /callback - 消息处理流程
    WX->>API: POST /callback<br/>携带加密的XML消息
    API->>API: 解析XML获取加密消息
    API->>Crypto: 验证签名并解密消息
    Crypto-->>API: 返回解密后的明文消息
    API->>Handler: 根据消息类型获取对应处理器
    
    alt 文本消息
        Handler->>MengChat: 调用process_message处理消息
        MengChat-->>Handler: 返回AI回复内容
        Handler->>DB: 记录消息和回复
        Handler->>WX: 发送回复消息(文本/markdown/图文)
    else 图片消息
        Handler->>DB: 记录图片消息
        Handler->>WX: 发送"暂不支持"提示
    else 其他类型消息
        Handler->>DB: 记录不支持的消息
        Handler->>WX: 发送"不支持"提示
    end
    
    API-->>WX: 返回success响应
