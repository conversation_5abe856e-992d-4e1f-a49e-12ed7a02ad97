sequenceDiagram
    participant <PERSON><PERSON> as MengChat应用管理平台
    participant <PERSON>gC<PERSON> as 企微MengChat
    participant WeChat as 企业微信官方
    participant User as 企业微信用户

    Admin->>+MengChat: 1. 发起消息推送请求
    Note over MengChat: 记录推送请求
    
    activate MengChat
    Note over MengChat: 2. 组装消息内容<br/>（文本、图片等）
    
    MengChat->>+WeChat: 3. 调用企业微信API推送消息
    Note over WeChat: 验证接口调用权限
    WeChat-->>-MengChat: 返回推送结果
    
    WeChat->>+User: 4. 推送消息到指定用户
    Note over User: 用户收到消息通知
    User-->>-WeChat: 消息送达确认
    
    deactivate MengChat
    Note over MengChat: 更新推送记录状态<br/>（成功/失败）
