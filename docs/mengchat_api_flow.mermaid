flowchart TD
    Start([开始]) --> SensitiveCheck{敏感词识别}
    
    %% 敏感词检查分支
    SensitiveCheck -->|不通过| End([结束])
    SensitiveCheck -->|通过| IntentRecog{意图识别}
    
    %% 意图识别分支
    IntentRecog -->|命中意图| IntentBranch{意图分类}
    IntentRecog -->|未命中意图| KnowledgeBase[知识库查询]
    
    %% 意图分支详情
    IntentBranch -->|代办流程查询| TodoQuery[访问OA系统查询代办流程]
    IntentBranch -->|已办流程查询| DoneQuery[访问OA系统查询已办流程]
    IntentBranch -->|流程状态查询| StatusQuery[访问OA系统查询流程详情]
    IntentBranch -->|新闻栏目查询| NewsQuery[访问OA系统查询新闻栏目]
    IntentBranch -->|归属业务员查询| SalesQuery[访问易保通商城查询归属业务员]
    
    %% 意图处理结果
    TodoQuery --> Response
    DoneQuery --> Response
    StatusQuery --> Response
    NewsQuery --> Response
    SalesQuery --> Response
    
    %% 知识库分支
    KnowledgeBase --> KnowledgeCheck{存在知识?}
    KnowledgeCheck -->|是| KnowledgeAnswer[基于知识回答问题]
    KnowledgeCheck -->|否| AIChat[基于AI助手闲聊回答]
    
    %% 最终响应
    KnowledgeAnswer --> Response[返回响应]
    AIChat --> Response
    Response --> End

    %% 样式定义
    classDef process fill:#f9f,stroke:#333,stroke-width:2px
    classDef condition fill:#bbf,stroke:#333,stroke-width:2px
    classDef endpoint fill:#9f9,stroke:#333,stroke-width:2px
    
    class SensitiveCheck,IntentRecog,IntentBranch,KnowledgeCheck condition
    class TodoQuery,DoneQuery,StatusQuery,NewsQuery,SalesQuery,KnowledgeBase,KnowledgeAnswer,AIChat process
    class Start,End,Response endpoint
