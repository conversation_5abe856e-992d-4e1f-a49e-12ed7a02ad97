# MengChat - 企业微信AI助手

<div align="center">

![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![Python](https://img.shields.io/badge/python-3.10+-blue.svg)
![FastAPI](https://img.shields.io/badge/FastAPI-0.95+-green.svg)

</div>

## 项目简介

MengChat 是一个基于 FastAPI 开发的智能企业微信助手，通过集成 MengChat AI 接口，为公司员工提供知识检索、流程查询、AI闲聊等智能功能。系统支持多种消息类型，包括文本、图片和图文混合消息，并能进行智能内容分析和回复。

## 核心特性

### 消息处理
- 企业微信消息接收与解析
  - 支持文本消息智能回复
  - 支持图片消息内容分析
  - 支持图文混合消息处理
- 安全消息通信
  - XML消息加解密
  - 签名验证机制
  - 令牌校验

### AI 能力
- MengChat AI 集成
  - 智能对话能力
  - 图片内容识别
  - 知识库查询
  - 流程状态查询
  - 新闻栏目查询
  - ...

### 内容转换
- Markdown 解析支持
- 图文消息生成
- 智能排版处理

## API 调用流程

系统采用多层级的消息处理流程，确保消息的安全性和智能处理的准确性。

![MengChat API Flow](docs/callback-流程.png)

### 流程说明

1. **敏感词识别**
   - 对输入内容进行敏感词检查
   - 通过检查继续处理，否则结束流程

2. **意图识别**
   - 分析用户输入，识别具体意图
   - 支持多种业务意图：
     - 代办流程查询（OA系统）
     - 已办流程查询（OA系统）
     - 流程状态查询（OA系统）
     - 新闻栏目查询（OA系统）
     - 归属业务员查询（易保通商城）

3. **知识库查询**
   - 未匹配到具体意图时，访问知识库
   - 存在相关知识时，基于知识内容回答
   - 无相关知识时，转为AI助手闲聊模式

![MengChat API Flow](docs/mengchat-流程.png)

## 技术栈

- 后端框架
  - Python 3.10+
  - FastAPI 0.105+
  - Pydantic 2.5+
  - uvicorn 0.24+

- 数据存储
  - SQLAlchemy 2.0+
  - PyMySQL 1.1+

- 核心依赖
  - loguru：日志管理
  - python-dotenv：环境配置
  - pydantic-settings：配置管理
  - requests：HTTP 客户端
  - cryptography：加解密功能
  - PyJWT：JWT 认证
  - pycryptodome：加密算法库
  - WeChatPy：企业微信SDK

## 快速开始

### 环境要求
- Python 3.10 或更高版本
- pip 包管理器
- 企业微信应用配置
- MengChat API 访问权限

### 安装步骤

1. 克隆项目
```bash
git clone http://gitlab.groupama-sdig.com/mengchat-app.git
cd mengchat-app
```

2. 创建并激活虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 在 Windows 上激活虚拟环境
.\venv\Scripts\activate

# 在 Linux/Mac 上激活虚拟环境
# source venv/bin/activate
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入必要的配置信息
```

5. 启动服务
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## 离线安装说明

如果生产环境无法访问外网，可以按照以下步骤进行离线安装：

1. 在有网络的环境下下载依赖包
```bash
# 创建packages目录
mkdir packages

# 下载所有依赖包到packages目录（Linux环境）
pip download -r requirements.txt -d ./packages --only-binary=:all: --platform manylinux2014_x86_64 --python-version 310 --no-deps
pip download -r requirements.txt -d ./packages --platform manylinux2014_x86_64 --python-version 310 --only-binary=:all:
```

2. 将项目打包
```bash
# 打包整个项目（排除venv目录）
tar -czf mengchat-app.tar.gz --exclude=venv *
```

3. 在生产环境中安装
```bash
# 解压项目文件
tar -xzf mengchat-app.tar.gz

# 创建并激活虚拟环境
python3 -m venv venv
source venv/bin/activate

# 从本地packages目录安装依赖
pip install --no-index --find-links ./packages -r requirements.txt
```

注意事项：
- 确保生产环境的 Python 版本为 3.10 或更高版本
- 如果安装过程中遇到权限问题，可能需要使用 sudo 或联系系统管理员
- 建议在与生产环境相似的测试环境中先验证安装过程

## 配置说明

### 企业微信配置
```env
CORP_ID=your_corp_id
AGENT_ID=your_agent_id
SECRET=your_secret
TOKEN=your_token
ENCODING_AES_KEY=your_encoding_aes_key
```

### MengChat 配置
```env
MENGCHAT_API_URL=your_mengchat_api_url
MENGCHAT_API_KEY=your_api_key
```

## 项目结构

```
app/
├── api/                    # API 接口层
│   └── endpoints/         # API 端点定义
│       ├── auth_api.py    # 认证相关接口
│       └── message_api.py # 消息处理接口
├── core/                  # 核心配置
│   └── config.py         # 应用配置
├── handlers/              # 处理器
│   └── message_handler.py # 消息处理器
├── models/               # 数据模型
│   ├── db.py            # 数据库配置
│   ├── message.py       # 消息模型
│   ├── message_processed.py # 消息处理记录
│   ├── message_record.py   # 消息历史记录
│   └── push_record.py    # 推送记录
├── services/            # 业务服务层
│   ├── mengchat_service.py # MengChat AI服务
│   ├── message_service.py  # 消息服务
│   └── wechat_service.py   # 企业微信服务
├── utils/              # 工具类
│   └── wechat_crypto.py # 企业微信加解密
└── main.py            # 应用入口
```

### 目录说明

- **api/**: API接口定义层
  - `endpoints/`: REST API端点实现
    - `auth_api.py`: 企业微信验证接口
    - `message_api.py`: 消息处理接口

- **core/**: 核心配置
  - `config.py`: 应用配置，包含企业微信和数据库等配置项

- **handlers/**: 消息处理器
  - `message_handler.py`: 实现消息的分发和处理逻辑

- **models/**: 数据模型层
  - `db.py`: SQLAlchemy数据库配置
  - `message.py`: 消息数据模型
  - `message_processed.py`: 消息处理记录模型
  - `message_record.py`: 消息历史记录模型
  - `push_record.py`: 消息推送记录模型

- **services/**: 业务服务层
  - `mengchat_service.py`: MengChat AI服务集成
  - `message_service.py`: 消息处理服务
  - `wechat_service.py`: 企业微信API服务

- **utils/**: 工具类
  - `wechat_crypto.py`: 企业微信消息加解密工具

- **main.py**: 应用程序入口，包含FastAPI应用实例和路由配置

## 安全说明

1. 消息安全
   - 所有消息都经过加密传输
   - 实现了完整的签名验证机制
   - 支持 Token 认证

2. 配置安全
   - 敏感配置通过环境变量管理
   - API 密钥妥善保管
   - 定期轮换安全密钥

## API 文档

启动服务后访问 `/docs` 或 `/redoc` 查看完整的 API 文档：
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 测试

运行测试套件：
```bash
pytest
```

## 日志管理

系统使用 loguru 进行日志管理，日志文件位于 `logs/` 目录：
- `app.log`: 应用日志
- `error.log`: 错误日志

## 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交改动 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 提交 Pull Request